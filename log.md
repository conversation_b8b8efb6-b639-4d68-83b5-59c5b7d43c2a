LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
LogTemp: Display: MCPServerRunnable: Client connection accepted
LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_map", "params": {"map_name": "AURACRON_Multilayer_Map", "layers": [{"name": "Planicie_Radiante", "height_offset": 1000, "layer_type": "ground", "streaming_distance": 5000, "description": "Reino da Luz e Crescimento - Camada Inferior com 3 lanes principais"}, {"name": "Firmamento_Zephyr", "height_offset": 3000, "layer_type": "aerial", "streaming_distance": 4000, "description": "Reino do Vento e Movimento - Camada M\u00e9dia com correntes de vento"}, {"name": "Abismo_Umbral", "height_offset": 5000, "layer_type": "undergro
und", "streaming_distance": 3000, "description": "Reino das Sombras e Mist\u00e9rio - Camada Superior labir\u00edntica"}], "world_partition_settings": {"enable_streaming": true, "cell_size": 6400, "loading_range": 12800, "enable_hlod": true}}}
LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_map
LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON_Multilayer_Map
LogStreaming: Display: FlushAsyncLoading(477): 1 QueuedPackages, 0 AsyncPackages
LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: WaitingForIo) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
LogStreaming: Display: Package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced is adding a dynamic import to package /Engine/Maps/Templates/HLODs/HLODLayer_Merged because of a recursive sync load
LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: DeferredPostLoad) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
LogWorldPartition: Display: WorldPartition initialize started...
LogWorldPartition: UWorldPartition::Initialize : World = /Game/Maps/AURACRON_Multilayer_Map.AURACRON_Multilayer_Map, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
LogWorldPartition: Display: WorldPartition initialize took 205 us (total: 228.254 ms)
LogTemp: [MapSystem] WorldPartition initialized successfully
LogTemp: [MapSystem] WorldPartition streaming enabled
LogTemp: [MapSystem] Configuring level for Data Layers support
LogTemp: [MapSystem] WorldPartition found - External Objects should be automatically enabled
LogTemp: [MapSystem] External Objects configuration attempted
LogTemp: [MapSystem] External Objects support verified through WorldPartition
LogTemp: [MapSystem] External Objects check via PersistentLevel: Not Supported
LogTemp: Warning: [MapSystem] Attempting to force enable External Actors...
LogTemp: [MapSystem] External Objects confirmed - proceeding with Data Layer creation
LogTemp: [MapSystem] Creating Data Layer: Planicie_Radiante (Index: 0)
LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Planicie_Radiante (Height: 1000.0, Type: ground, State: Activated)
LogTemp: [MapSystem] Creating Data Layer: Firmamento_Zephyr (Index: 1)
LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Firmamento_Zephyr (Height: 3000.0, Type: aerial, State: Loaded)
LogTemp: [MapSystem] Creating Data Layer: Abismo_Umbral (Index: 2)
LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Abismo_Umbral (Height: 5000.0, Type: underground, State: Loaded)
LogTemp: [MapSystem] WorldPartition streaming configured via subsystem
LogTemp: [MapSystem] World Partition configured SAFELY with 3 Data Layers (Created: 3, Valid Instances: 3)
LogFileHelpers: InternalPromptForCheckoutAndSave started...
Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/AURACRON_Multilayer_Map" FILE="../../../../../../Game/AURACRON/Content/Maps/AURACRON_Multilayer_Map.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
LogUObjectHash: Compacting FUObjectHashTables data took   0.54ms
LogSavePackage: Moving output files for package: /Game/Maps/AURACRON_Multilayer_Map
LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Multilayer_Map04A1C34D473D605A28D7C6A4BC77104C.tmp' to '../../../../../../Game/AURACRON/Content/Maps/AURACRON_Multilayer_Map.umap'
LogFileHelpers: Saving map 'AURACRON_Multilayer_Map' took 0.042
LogFileHelpers: InternalPromptForCheckoutAndSave took 112.174 ms
LogTemp: AURACRON: DataLayer Planicie_Radiante accessed SAFELY
LogTemp: AURACRON: DataLayer Firmamento_Zephyr accessed SAFELY
LogTemp: AURACRON: DataLayer Abismo_Umbral accessed SAFELY
LogTemp: [MapSystem] Multilayer map created successfully:
LogTemp:   - Map Name: AURACRON_Multilayer_Map
LogTemp:   - Main Asset: /Game/Maps/AURACRON_Multilayer_Map
LogTemp:   - Layers Created: 3
LogTemp:   - World Partition: Enabled
LogTemp:   - Saved to Disk: SUCCESS
LogTemp: Display: MCPServerRunnable: Sending response: {
    "status": "success",
    "result":
    {
        "command": "create_multilayer_map",
        "map_name": "AURACRON_Multilayer_Map",
        "main_asset_path": "/Game/Maps/AURACRON_Multilayer_Map",
        "saved_to_disk": true,
        "full_disk_path": "../../../../../../Game/AURACRON/Content/Maps/AURACRON_Multilayer_Map.umap",
        "size_x": 10000,
        "size_y": 10000,
        "layers_created": [
            {
                "name": "Planicie_Radiante",
                "height_offset": 1000,
                "layer_type": "ground",
                "streaming_distance": 5000,
                "description": "Reino da Luz e Crescimento - Camada Inferior com 3 lanes principais",
                "data_layer_name": "Planicie_Radiante",
                "is_visible": true,
                "is_initially_visible": true,
                "runtime_state": "Activated",
                "data_layer_created": true
            },
            {
                "name": "Firmamento_Zephyr",
                "height_offset": 3000,
                "layer_type": "aerial",
                "streaming_distance": 4000,
                "description": "Reino do Vento e Movimento - Camada Média com correntes de vento",
                "data_layer_name": "Firmamento_Zephyr",
                "is_visible": true,
                "is_initially_visible": true,
                "runtime_state": "Loaded",
                "data_layer_created": true
            },
            {
                "name": "Abismo_Umbral",
                "height_offset": 5000,
                "layer_type": "underground",
                "streaming_distance": 3000,
                "description": "Reino das Sombras e Mistério - Camada Superior labiríntica",
                "data_layer_name": "Abismo_Umbral",
                "is_visible": true,
                "is_initially_visible": true,
                "runtime_state": "Loaded",
                "data_layer_created": true
            }
        ],
        "world_partition_enabled": "true",
        "world_data_layers_created": true,
        "data_layer_instances_count": 3,
        "cell_size": 25600,
        "loading_range": 76800,
        "timestamp": "2025.08.27-21.05.18"
    }
}
LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1759
LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
LogContentValidation: Display: Starting to validate 1 assets
LogContentValidation: Enabled validators:
LogContentValidation:     /Script/DataValidation.EditorValidator_Material
LogContentValidation:     /Script/DataValidation.DirtyFilesChangelistValidator
LogContentValidation:     /Script/DataValidation.EditorValidator_Localization
LogContentValidation:     /Script/DataValidation.PackageFileValidator
LogContentValidation:     /Script/DataValidation.WorldPartitionChangelistValidator
LogContentValidation:     /Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
AssetCheck: /Game/Maps/AURACRON_Multilayer_Map Validando ativo