/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MovieScene/MovieScene.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
/NATVIS:"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/PCG.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/GeometryCore.natvis"
/NATVIS:"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/Niagara.natvis"
"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Module.UnrealMCP.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/MCPServerRunnable.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBridge.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPModule.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPAnalyticsCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPArchitectureCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBalanceCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBlueprintCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPBlueprintNodeCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCollisionAdvancedCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCollisionCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPCommonUtils.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPEditorCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPLandscapeCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMapCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMaterialCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPMOBACommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPPathfindingCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPProceduralMeshCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPProjectCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPUMGCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPVisionCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCPVisualEffectsCommands.cpp.obj"
"C:/Game/AURACRON/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/UnrealEditor-UnrealEd.lib"
"../Plugins/Editor/EditorScriptingUtilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorScriptingUtilities/UnrealEditor-EditorScriptingUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorSubsystem/UnrealEditor-EditorSubsystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMG/UnrealEditor-UMG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Kismet/UnrealEditor-Kismet.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/KismetCompiler/UnrealEditor-KismetCompiler.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/BlueprintGraph/UnrealEditor-BlueprintGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Projects/UnrealEditor-Projects.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AssetRegistry/UnrealEditor-AssetRegistry.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Renderer/UnrealEditor-Renderer.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PhysicsCore/UnrealEditor-PhysicsCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Chaos/UnrealEditor-Chaos.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LevelSequence/UnrealEditor-LevelSequence.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MovieScene/UnrealEditor-MovieScene.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AssetTools/UnrealEditor-AssetTools.lib"
"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCGCompute/UnrealEditor-PCGCompute.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Voronoi/UnrealEditor-Voronoi.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshModelingTools/UnrealEditor-MeshModelingTools.lib"
"../Plugins/Experimental/MeshModelingToolsetExp/Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshModelingToolsExp/UnrealEditor-MeshModelingToolsExp.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModelingOperators/UnrealEditor-ModelingOperators.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModelingOperatorsEditorOnly/UnrealEditor-ModelingOperatorsEditorOnly.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraEditor/UnrealEditor-NiagaraEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PropertyEditor/UnrealEditor-PropertyEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/BlueprintEditorLibrary/UnrealEditor-BlueprintEditorLibrary.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMGEditor/UnrealEditor-UMGEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LandscapeEditor/UnrealEditor-LandscapeEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/WorldPartitionEditor/UnrealEditor-WorldPartitionEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DataLayerEditor/UnrealEditor-DataLayerEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Networking/UnrealEditor-Networking.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Sockets/UnrealEditor-Sockets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/HTTP/UnrealEditor-HTTP.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Json/UnrealEditor-Json.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/JsonUtilities/UnrealEditor-JsonUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperSettings/UnrealEditor-DeveloperSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ImageCore/UnrealEditor-ImageCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ImageWrapper/UnrealEditor-ImageWrapper.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Landscape/UnrealEditor-Landscape.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LevelEditor/UnrealEditor-LevelEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NavigationSystem/UnrealEditor-NavigationSystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AIModule/UnrealEditor-AIModule.lib"
"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/UnrealEditor-PCG.lib"
"../Plugins/Runtime/ComputeFramework/Intermediate/Build/Win64/x64/UnrealEditor/Development/ComputeFramework/UnrealEditor-ComputeFramework.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Foliage/UnrealEditor-Foliage.lib"
"../Plugins/Experimental/LandscapePatch/Intermediate/Build/Win64/x64/UnrealEditor/Development/LandscapePatch/UnrealEditor-LandscapePatch.lib"
"../Plugins/Experimental/GeometryFlow/Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryFlowCore/UnrealEditor-GeometryFlowCore.lib"
"../Plugins/Experimental/GeometryFlow/Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryFlowMeshProcessing/UnrealEditor-GeometryFlowMeshProcessing.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/UnrealEditor-GeometryCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryFramework/UnrealEditor-GeometryFramework.lib"
"../Plugins/Runtime/GeometryProcessing/Intermediate/Build/Win64/x64/UnrealEditor/Development/DynamicMesh/UnrealEditor-DynamicMesh.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshDescription/UnrealEditor-MeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/StaticMeshDescription/UnrealEditor-StaticMeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshConversion/UnrealEditor-MeshConversion.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MaterialEditor/UnrealEditor-MaterialEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorStyle/UnrealEditor-EditorStyle.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorWidgets/UnrealEditor-EditorWidgets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Analytics/UnrealEditor-Analytics.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnalyticsET/UnrealEditor-AnalyticsET.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/TelemetryUtils/UnrealEditor-TelemetryUtils.lib"
"../Plugins/AI/MLAdapter/Intermediate/Build/Win64/x64/UnrealEditor/Development/MLAdapter/UnrealEditor-MLAdapter.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosCore/UnrealEditor-ChaosCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosSolverEngine/UnrealEditor-ChaosSolverEngine.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraCore/UnrealEditor-NiagaraCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraShader/UnrealEditor-NiagaraShader.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/UnrealEditor-Niagara.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
"legacy_stdio_definitions.lib"
/OUT:"C:/Game/AURACRON/Plugins/UnrealMCP/Binaries/Win64/UnrealEditor-UnrealMCP.dll"
/PDB:"C:/Game/AURACRON/Plugins/UnrealMCP/Binaries/Win64/UnrealEditor-UnrealMCP.pdb"
/ignore:4078