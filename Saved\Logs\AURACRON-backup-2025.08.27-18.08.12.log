﻿Log file open, 08/27/25 14:47:35
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=19812)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 39934
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:/Game/AURACRON/AURACRON.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=692f8dfb3e6d4466a947ee76dbcd1b51 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.293630
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-E9E9DBC74FE8EF152E392E8E81499D39
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
LogConfig: Display: Loading VulkanPC ini files took 0.09 seconds
LogConfig: Display: Loading Mac ini files took 0.09 seconds
LogAssetRegistry: Display: Asset registry cache read as 73.0 MiB from ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin ChaosVD
LogConfig: Display: Loading Android ini files took 0.10 seconds
LogConfig: Display: Loading IOS ini files took 0.10 seconds
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin MLAdapter
LogPluginManager: Mounting Engine plugin ACLPlugin
LogConfig: Display: Loading Unix ini files took 0.11 seconds
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogConfig: Display: Loading Linux ini files took 0.12 seconds
LogConfig: Display: Loading TVOS ini files took 0.12 seconds
LogConfig: Display: Loading Windows ini files took 0.12 seconds
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin Fracture
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LandscapePatch
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -AUTH_LOGIN=unused -AUTH_PASSWORD=692f8dfb3e6d4466a947ee76dbcd1b51 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.27-17.47.36:091][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.27-17.47.36:091][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.27-17.47.36:091][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.27-17.47.36:091][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.27-17.47.36:091][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.27-17.47.36:092][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.08.27-17.47.36:092][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.27-17.47.36:093][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.27-17.47.36:093][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.27-17.47.36:094][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.27-17.47.36:094][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.27-17.47.36:094][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.27-17.47.36:094][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.27-17.47.36:096][  0]LogRHI: Using Default RHI: D3D12
[2025.08.27-17.47.36:096][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.27-17.47.36:096][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.27-17.47.36:098][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.27-17.47.36:098][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.27-17.47.36:199][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.27-17.47.36:220][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.27-17.47.36:220][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.27-17.47.36:220][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.27-17.47.36:220][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.27-17.47.36:220][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.27-17.47.36:226][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.27-17.47.36:226][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.27-17.47.36:226][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.27-17.47.36:226][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.27-17.47.36:226][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.27-17.47.36:226][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.27-17.47.36:226][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.27-17.47.36:226][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.27-17.47.36:227][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.27-17.47.36:228][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.27-17.47.36:228][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.27-17.47.36:228][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.27-17.47.36:228][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.27-17.47.36:228][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.27-17.47.36:427][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.27-17.47.36:427][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.27-17.47.36:428][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.27-17.47.36:428][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.27-17.47.36:428][  0]LogD3D11RHI:     AdapterLuid : 0 86560
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.27-17.47.36:431][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.27-17.47.36:431][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.27-17.47.36:431][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.27-17.47.36:431][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.27-17.47.36:431][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.27-17.47.36:431][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.27-17.47.36:431][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.27-17.47.36:431][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.27-17.47.36:431][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.27-17.47.36:431][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.27-17.47.36:431][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.27-17.47.36:432][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.27-17.47.36:432][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.27-17.47.36:432][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.27-17.47.36:432][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.27-17.47.36:432][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.27-17.47.36:432][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.27-17.47.36:432][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.27-17.47.36:432][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/AURACRON/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.27-17.47.36:432][  0]LogInit: Computer: TKT
[2025.08.27-17.47.36:432][  0]LogInit: User: tktca
[2025.08.27-17.47.36:432][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.27-17.47.36:432][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.27-17.47.36:432][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.27-17.47.36:432][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=36.9GB
[2025.08.27-17.47.36:432][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.27-17.47.36:432][  0]LogMemory: Process Physical Memory: 644.25 MB used, 707.22 MB peak
[2025.08.27-17.47.36:432][  0]LogMemory: Process Virtual Memory: 647.72 MB used, 692.64 MB peak
[2025.08.27-17.47.36:432][  0]LogMemory: Physical Memory: 19749.07 MB used,  12703.63 MB free, 32452.70 MB total
[2025.08.27-17.47.36:432][  0]LogMemory: Virtual Memory: 27837.09 MB used,  9904.74 MB free, 37741.82 MB total
[2025.08.27-17.47.36:432][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.27-17.47.36:435][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.27-17.47.36:437][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.27-17.47.36:437][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.27-17.47.36:437][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.27-17.47.36:437][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.27-17.47.36:442][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.27-17.47.36:735][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.27-17.47.36:736][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.27-17.47.36:736][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.27-17.47.36:736][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.27-17.47.36:736][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.27-17.47.36:736][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.27-17.47.36:830][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.27-17.47.36:830][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.08.27-17.47.36:830][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.08.27-17.47.36:830][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.27-17.47.36:834][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.08.27-17.47.36:837][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.27-17.47.36:842][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.27-17.47.36:842][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.27-17.47.36:938][  0]LogRHI: Using Default RHI: D3D12
[2025.08.27-17.47.36:938][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.27-17.47.36:938][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.27-17.47.36:938][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.27-17.47.36:938][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.27-17.47.36:938][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.27-17.47.36:938][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.27-17.47.36:938][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.27-17.47.36:938][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.27-17.47.36:939][  0]LogWindows: Attached monitors:
[2025.08.27-17.47.36:939][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY1' [PRIMARY]
[2025.08.27-17.47.36:939][  0]LogWindows: Found 1 attached monitors.
[2025.08.27-17.47.36:939][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.27-17.47.36:939][  0]LogRHI: RHI Adapter Info:
[2025.08.27-17.47.36:939][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.27-17.47.36:939][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.27-17.47.36:939][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.27-17.47.36:939][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.27-17.47.36:939][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.27-17.47.36:939][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.27-17.47.36:939][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.27-17.47.36:939][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.27-17.47.37:115][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.27-17.47.37:141][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.27-17.47.37:142][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.27-17.47.37:142][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.27-17.47.37:142][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.27-17.47.37:142][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.27-17.47.37:142][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.27-17.47.37:142][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.27-17.47.37:153][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.27-17.47.37:156][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.27-17.47.37:156][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.27-17.47.37:156][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.27-17.47.37:156][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.27-17.47.37:156][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.27-17.47.37:157][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all'
[2025.08.27-17.47.37:158][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all" ]
[2025.08.27-17.47.37:169][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.27-17.47.37:169][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.27-17.47.37:169][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.27-17.47.37:169][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.27-17.47.37:169][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.27-17.47.37:169][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.27-17.47.37:169][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.27-17.47.37:169][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.27-17.47.37:169][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.27-17.47.37:170][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.27-17.47.37:195][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.27-17.47.37:207][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.27-17.47.37:207][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.27-17.47.37:218][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.27-17.47.37:218][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.27-17.47.37:218][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.27-17.47.37:219][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.27-17.47.37:229][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.27-17.47.37:229][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.27-17.47.37:229][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.27-17.47.37:229][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.27-17.47.37:240][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.27-17.47.37:240][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.27-17.47.37:254][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.27-17.47.37:254][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.27-17.47.37:254][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.27-17.47.37:254][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.27-17.47.37:254][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.27-17.47.37:286][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.27-17.47.37:290][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.27-17.47.37:290][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.27-17.47.37:292][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.27-17.47.37:292][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.27-17.47.37:293][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.27-17.47.37:293][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.27-17.47.37:293][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.27-17.47.37:483][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.27-17.47.37:483][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.27-17.47.37:483][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.27-17.47.37:485][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.27-17.47.37:486][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.27-17.47.37:486][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.27-17.47.37:486][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.08.27-17.47.37:890][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.27-17.47.37:890][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.405 seconds
[2025.08.27-17.47.37:892][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.27-17.47.37:901][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.08.27-17.47.37:902][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.03ms. RandomReadSpeed=1483.46MBs, RandomWriteSpeed=119.58MBs. Assigned SpeedClass 'Local'
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.27-17.47.37:903][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.27-17.47.37:904][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/AURACRON/Intermediate/Shaders/WorkingDirectory/26216/).
[2025.08.27-17.47.37:904][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/3D8A5AEF4B7FF6A591FD729E82FE5C0F/'.
[2025.08.27-17.47.37:904][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.27-17.47.37:905][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.27-17.47.37:905][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.27-17.47.37:907][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/AURACRON/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.27-17.47.37:908][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.27-17.47.38:346][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.27-17.47.39:074][  0]LogSlate: Using FreeType 2.10.0
[2025.08.27-17.47.39:075][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.27-17.47.39:077][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.27-17.47.39:077][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.27-17.47.39:077][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.27-17.47.39:077][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.27-17.47.39:090][  0]LogAssetRegistry: FAssetRegistry took 0.0024 seconds to start up
[2025.08.27-17.47.39:092][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.27-17.47.39:159][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.27-17.47.39:304][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.27-17.47.39:304][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.27-17.47.39:332][  0]LogDeviceProfileManager: Active device profile: [00000236BC975000][00000236AA825000 66] WindowsEditor
[2025.08.27-17.47.39:332][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.27-17.47.39:334][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.27-17.47.39:336][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.27-17.47.39:336][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.27-17.47.39:336][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.27-17.47.39:342][  0]LogTurnkeySupport: Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
[2025.08.27-17.47.39:343][  0]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.27-17.47.39:343][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.08.27-17.47.39:343][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT'
[2025.08.27-17.47.39:343][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.08.27-17.47.39:372][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:373][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.27-17.47.39:373][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:373][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:374][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.27-17.47.39:374][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:374][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:376][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.27-17.47.39:376][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.27-17.47.39:378][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.27-17.47.39:379][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:379][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:379][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:380][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.27-17.47.39:380][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.27-17.47.39:382][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.27-17.47.39:383][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:412][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.27-17.47.39:412][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:427][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.27-17.47.39:427][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:438][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.27-17.47.39:438][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.27-17.47.39:540][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.27-17.47.39:540][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.27-17.47.39:540][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.27-17.47.39:540][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.27-17.47.39:540][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.27-17.47.39:938][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.27-17.47.39:969][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.27-17.47.39:969][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.27-17.47.39:969][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.27-17.47.39:969][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.27-17.47.39:971][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.27-17.47.39:971][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.27-17.47.39:972][  0]LogLiveCoding: Display: First instance in process group "UE_AURACRON_0xa5ca6502", spawning console
[2025.08.27-17.47.39:975][  0]LogLiveCoding: Display: Waiting for server
[2025.08.27-17.47.39:989][  0]LogSlate: Border
[2025.08.27-17.47.39:989][  0]LogSlate: BreadcrumbButton
[2025.08.27-17.47.39:989][  0]LogSlate: Brushes.Title
[2025.08.27-17.47.39:989][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.27-17.47.39:989][  0]LogSlate: Default
[2025.08.27-17.47.39:989][  0]LogSlate: Icons.Save
[2025.08.27-17.47.39:989][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.27-17.47.39:989][  0]LogSlate: ListView
[2025.08.27-17.47.39:989][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.27-17.47.39:989][  0]LogSlate: SoftwareCursor_Grab
[2025.08.27-17.47.39:989][  0]LogSlate: TableView.DarkRow
[2025.08.27-17.47.39:989][  0]LogSlate: TableView.Row
[2025.08.27-17.47.39:989][  0]LogSlate: TreeView
[2025.08.27-17.47.40:065][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.27-17.47.40:067][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.147 ms
[2025.08.27-17.47.40:080][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.27-17.47.40:080][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.27-17.47.40:080][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.27-17.47.40:106][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.27-17.47.40:133][  0]LogMLAdapter: Warning: Neural network asset data not set
[2025.08.27-17.47.40:424][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.27-17.47.40:427][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.27-17.47.40:427][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.27-17.47.40:427][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:55912'.
[2025.08.27-17.47.40:431][  0]LogUdpMessaging: Display: Added local interface '192.168.3.31' to multicast group '230.0.0.1:6666'
[2025.08.27-17.47.40:431][  0]LogUdpMessaging: Display: Added local interface '172.17.96.1' to multicast group '230.0.0.1:6666'
[2025.08.27-17.47.40:433][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.08.27-17.47.40:443][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 87B4C008EE87470C8000000000007700 | Instance: A5590F8E48BDDB19B46742846E11A5F2 (TKT-26216).
[2025.08.27-17.47.40:503][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.27-17.47.40:594][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.27-17.47.40:594][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT: Interface availability:
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   RDG: no
[2025.08.27-17.47.40:665][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.27-17.47.40:734][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.27-17.47.40:734][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.27-17.47.40:900][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.27-17.47.40:900][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.27-17.47.40:914][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.27-17.47.40:932][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.27-17.47.40:961][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.27-17.47.40:961][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.27-17.47.40:969][  0]LogTimingProfiler: Initialize
[2025.08.27-17.47.40:969][  0]LogTimingProfiler: OnSessionChanged
[2025.08.27-17.47.40:969][  0]LoadingProfiler: Initialize
[2025.08.27-17.47.40:969][  0]LoadingProfiler: OnSessionChanged
[2025.08.27-17.47.40:970][  0]LogNetworkingProfiler: Initialize
[2025.08.27-17.47.40:970][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.27-17.47.40:970][  0]LogMemoryProfiler: Initialize
[2025.08.27-17.47.40:970][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.27-17.47.40:997][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.27-17.47.41:243][  0]SourceControl: Controle de revisão desabilitado
[2025.08.27-17.47.41:250][  0]SourceControl: Controle de revisão desabilitado
[2025.08.27-17.47.41:251][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.27-17.47.41:251][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.27-17.47.41:251][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.27-17.47.41:251][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.27-17.47.41:280][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.27-17.47.41:287][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.27-17.47.41:290][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.27-17.47.41:290][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.27-17.47.41:326][  0]LogCollectionManager: Loaded 0 collections in 0.001240 seconds
[2025.08.27-17.47.41:328][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Saved/Collections/' took 0.00s
[2025.08.27-17.47.41:331][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Developers/tktca/Collections/' took 0.00s
[2025.08.27-17.47.41:334][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Collections/' took 0.00s
[2025.08.27-17.47.41:361][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.27-17.47.41:397][  0]LogUObjectArray: 44823 objects as part of root set at end of initial load.
[2025.08.27-17.47.41:397][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.27-17.47.41:491][  0]LogAutomationTest: Error: Condition failed
[2025.08.27-17.47.41:491][  0]LogAutomationTest: Error: Condition failed
[2025.08.27-17.47.41:491][  0]LogAutomationTest: Error: Condition failed
[2025.08.27-17.47.41:491][  0]LogEngine: Initializing Engine...
[2025.08.27-17.47.41:591][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.27-17.47.41:592][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.27-17.47.41:592][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.27-17.47.41:592][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.27-17.47.41:592][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.27-17.47.41:594][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.27-17.47.41:783][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.27-17.47.41:799][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.27-17.47.41:812][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.27-17.47.41:827][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.08.27-17.47.41:827][  0]LogInit: Texture streaming: Enabled
[2025.08.27-17.47.41:835][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.08.27-17.47.41:839][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.27-17.47.41:844][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.27-17.47.41:845][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.27-17.47.41:845][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.27-17.47.41:845][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.27-17.47.41:845][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.27-17.47.41:845][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.27-17.47.41:845][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.27-17.47.41:845][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.27-17.47.41:845][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.27-17.47.41:851][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.27-17.47.42:217][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (Realtek(R) Audio)
[2025.08.27-17.47.42:217][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.27-17.47.42:219][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.27-17.47.42:219][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.27-17.47.42:219][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.27-17.47.42:219][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.27-17.47.42:221][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.27-17.47.42:221][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.27-17.47.42:221][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.27-17.47.42:221][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.27-17.47.42:221][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.27-17.47.42:227][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.27-17.47.42:234][  0]LogInit: Undo buffer set to 256 MB
[2025.08.27-17.47.42:234][  0]LogInit: Transaction tracking system initialized
[2025.08.27-17.47.42:241][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.27-17.47.42:270][  0]LocalizationService: O serviço de localização está desativado.
[2025.08.27-17.47.42:302][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.08.27-17.47.42:390][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/' took 0.00s
[2025.08.27-17.47.42:408][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.27-17.47.42:427][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.27-17.47.42:427][  0]LogPython: Using Python 3.11.8
[2025.08.27-17.47.42:449][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.27-17.47.42:792][  0]LogMLAdapter: Creating MLAdapter manager of class MLAdapterManager
[2025.08.27-17.47.42:854][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.27-17.47.42:874][  0]LogEditorDataStorage: Initializing
[2025.08.27-17.47.42:876][  0]LogEditorDataStorage: Initialized
[2025.08.27-17.47.42:878][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.08.27-17.47.42:880][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.08.27-17.47.42:942][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.27-17.47.42:945][  0]SourceControl: Controle de revisão desabilitado
[2025.08.27-17.47.42:945][  0]LogUnrealEdMisc: Loading editor; pre map load, took 7.613
[2025.08.27-17.47.42:946][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.27-17.47.42:948][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.27-17.47.42:949][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.27-17.47.42:949][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.27-17.47.42:966][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.27-17.47.42:967][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.49ms
[2025.08.27-17.47.42:974][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.27-17.47.42:974][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.27-17.47.42:976][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.27-17.47.42:976][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.27-17.47.42:976][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.27-17.47.43:088][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.27-17.47.43:094][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.27-17.47.43:098][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.27-17.47.43:102][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.27-17.47.43:102][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.27-17.47.43:102][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.27-17.47.43:106][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.27-17.47.43:220][  0]LogWorldPartition: Display: WorldPartition initialize took 243.985 ms
[2025.08.27-17.47.43:354][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.27-17.47.43:381][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.08.27-17.47.43:381][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.27-17.47.43:382][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 1,184ms para ser concluída.
[2025.08.27-17.47.43:388][  0]LogUnrealEdMisc: Total Editor Startup Time, took 8.057
[2025.08.27-17.47.43:514][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.08.27-17.47.43:529][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.27-17.47.43:838][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.27-17.47.44:124][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.27-17.47.44:397][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.27-17.47.44:574][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.27-17.47.44:886][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:887][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.27-17.47.44:888][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:888][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.27-17.47.44:889][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:889][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.27-17.47.44:890][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:890][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.08.27-17.47.44:891][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:892][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.27-17.47.44:892][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:894][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.27-17.47.44:895][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:895][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.27-17.47.44:896][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:896][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.27-17.47.44:897][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:897][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.27-17.47.44:898][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.27-17.47.44:899][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.27-17.47.45:363][  0]LogAssetRegistry: Display: Asset registry cache written as 73.0 MiB to ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin
[2025.08.27-17.47.46:013][  0]LogSlate: Took 0.000445 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.27-17.47.46:017][  0]LogSlate: Took 0.000170 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.27-17.47.46:065][  0]LogSlate: Took 0.000220 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.27-17.47.46:195][  0]LogStall: Startup...
[2025.08.27-17.47.46:198][  0]LogStall: Startup complete.
[2025.08.27-17.47.46:203][  0]LogLoad: (Engine Initialization) Total time: 10.87 seconds
[2025.08.27-17.47.46:791][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.27-17.47.46:791][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.27-17.47.46:840][  0]LogAutomationController: Ignoring very large delta of 16879989.97 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.08.27-17.47.46:840][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.27-17.47.46:842][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.27-17.47.46:875][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.27-17.47.46:884][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 41.289 ms
[2025.08.27-17.47.47:116][  1]LogAssetRegistry: AssetRegistryGather time 0.1253s: AssetDataDiscovery 0.0119s, AssetDataGather 0.0401s, StoreResults 0.0734s. Wall time 8.0280s.
	NumCachedDirectories 1419. NumUncachedDirectories 22. NumCachedFiles 7457. NumUncachedFiles 1.
	BackgroundTickInterruptions 0.
[2025.08.27-17.47.47:140][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.27-17.47.47:148][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.27-17.47.47:148][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.08.27-17.47.47:450][  3]LogSourceControl: Uncontrolled asset discovery finished in 0.302728 seconds (Found 7434 uncontrolled assets)
[2025.08.27-17.48.41:868][244]LogSlate: Took 0.000218 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.27-17.48.43:243][259]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.08.27-17.48.43:246][259]Cmd: MAP LOAD FILE="../../../../../../Game/AURACRON/Content/AURACRON.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.27-17.48.43:271][259]LogWorld: UWorld::CleanupWorld for Untitled_1, bSessionEnded=true, bCleanupResources=true
[2025.08.27-17.48.43:272][259]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.27-17.48.43:272][259]LogWorldPartition: UWorldPartition::Uninitialize : World = /Temp/Untitled_1.Untitled_1
[2025.08.27-17.48.43:291][259]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.27-17.48.43:301][259]LogUObjectHash: Compacting FUObjectHashTables data took   0.45ms
[2025.08.27-17.48.43:302][259]LogStreaming: Display: FlushAsyncLoading(476): 1 QueuedPackages, 0 AsyncPackages
[2025.08.27-17.48.43:303][259]LogAudio: Display: Audio Device (ID: 1) registered with world 'AURACRON'.
[2025.08.27-17.48.43:303][259]LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON
[2025.08.27-17.48.43:329][259]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.27-17.48.43:347][259]LogUObjectHash: Compacting FUObjectHashTables data took   0.41ms
[2025.08.27-17.48.43:353][259]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.27-17.48.43:353][259]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 0,068ms para ser concluída.
[2025.08.27-17.48.43:355][259]LogSlate: Window 'Abrir nível' being destroyed
[2025.08.27-17.48.53:567][426]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.48.53:567][426]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.48.53:567][426]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.48.53:668][426]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.48.53:668][426]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.48.53:668][426]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_map", "params": {"map_name": "AURACRON_MainMap", "layers": [{"name": "Planicie_Radiante", "height_offset": 1000, "layer_type": "ground", "streaming_distance": 18000, "description": "Reino da Luz e Crescimento - Camada Inferior com 3 lanes principais"}, {"name": "Firmamento_Zephyr", "height_offset": 3000, "layer_type": "aerial", "streaming_distance": 18000, "description": "Reino do Vento e Movimento - Camada M\u00e9dia com correntes de vento"}, {"name": "Abismo_Umbral", "height_offset": 5000, "layer_type": "underground", "streaming_distance": 18000, "description": "Reino das Sombras e Mist\u00e9rio - Camada Superior labir\u00edntica"}], "world_partition_settings": {"enable_streaming": true, "cell_size": 6000, "loading_range": 12000, "enable_hlod": true}}}
[2025.08.27-17.48.53:668][426]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_map
[2025.08.27-17.48.53:796][426]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Map already exists: AURACRON_MainMap"
}
[2025.08.27-17.48.53:796][426]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 75
[2025.08.27-17.48.53:796][426]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.48.58:023][489]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.48.58:023][489]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.48.58:023][489]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.48.58:124][492]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.48.58:124][492]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.48.58:124][492]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.27-17.48.58:124][492]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.27-17.48.58:124][492]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: get_actors_in_level
[2025.08.27-17.48.58:125][492]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		]
	}
}
[2025.08.27-17.48.58:125][492]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1072
[2025.08.27-17.48.58:125][492]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.49.10:708][535]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.49.10:708][535]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.49.10:708][535]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.49.10:809][536]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.49.10:809][536]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.49.10:809][536]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "Planicie_Radiante_Terrain", "layer_index": 0, "size_x": 1009, "size_y": 1009, "scale_x": 100.0, "scale_y": 100.0, "scale_z": 100.0, "location": {"x": 0.0, "y": 0.0, "z": 1000.0}, "heightmap_settings": {"noise_type": "perlin", "frequency": 0.01, "amplitude": 200, "octaves": 4, "lacunarity": 2, "persistence": 0.5}}}
[2025.08.27-17.49.10:809][536]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.27-17.49.11:053][536]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.27-17.49.11:054][536]LogJson: Warning: Field use_pcg was not found.
[2025.08.27-17.49.11:054][536]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.49.11:054][536]LogJson: Warning: Field enable_nanite was not found.
[2025.08.27-17.49.11:054][536]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.49.11:054][536]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.27-17.49.11:054][536]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.49.11:086][536]LogTemp: CreateLayerMaterial: Created material for layer 0 with theme color (R=1.000000,G=0.800000,B=0.200000,A=1.000000)
[2025.08.27-17.49.11:086][536]LogTemp: CreateRobustAuracronLandscape: Created landscape Planicie_Radiante_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.49.11:116][536]LogTemp: CreateLayerMaterial: Created material for layer 1 with theme color (R=0.200000,G=0.800000,B=1.000000,A=1.000000)
[2025.08.27-17.49.11:116][536]LogTemp: CreateRobustAuracronLandscape: Created landscape Planicie_Radiante_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.49.11:146][536]LogTemp: CreateLayerMaterial: Created material for layer 2 with theme color (R=0.400000,G=0.200000,B=0.800000,A=1.000000)
[2025.08.27-17.49.11:146][536]LogTemp: CreateRobustAuracronLandscape: Created landscape Planicie_Radiante_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.49.11:147][536]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for Planicie_Radiante_Terrain
[2025.08.27-17.49.11:147][536]LogTemp: HandleCreateProceduralLandscape: Created landscape Planicie_Radiante_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.27-17.49.11:147][536]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "Planicie_Radiante_Terrain",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-14.49.11"
	}
}
[2025.08.27-17.49.11:147][536]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 336
[2025.08.27-17.49.11:147][536]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.49.37:905][617]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.27-17.52.43:054][172]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.52.43:054][172]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.52.43:054][172]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.52.43:155][173]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.52.43:155][173]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.52.43:155][173]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "Firmamento_Zephyr_Terrain", "layer_index": 1, "size_x": 1009, "size_y": 1009, "scale_x": 100.0, "scale_y": 100.0, "scale_z": 100.0, "location": {"z": 3000.0}, "heightmap_settings": {"octaves": 6, "persistence": 0.4, "noise_type": "simplex", "amplitude": 300, "lacunarity": 2.5, "frequency": 0.008}}}
[2025.08.27-17.52.43:155][173]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.27-17.52.43:421][173]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.27-17.52.43:421][173]LogJson: Warning: Field use_pcg was not found.
[2025.08.27-17.52.43:421][173]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.52.43:421][173]LogJson: Warning: Field enable_nanite was not found.
[2025.08.27-17.52.43:421][173]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.52.43:421][173]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.27-17.52.43:421][173]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.52.43:451][173]LogTemp: CreateRobustAuracronLandscape: Created landscape Firmamento_Zephyr_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.52.43:481][173]LogTemp: CreateRobustAuracronLandscape: Created landscape Firmamento_Zephyr_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.52.43:510][173]LogTemp: CreateRobustAuracronLandscape: Created landscape Firmamento_Zephyr_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.52.43:510][173]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for Firmamento_Zephyr_Terrain
[2025.08.27-17.52.43:510][173]LogTemp: HandleCreateProceduralLandscape: Created landscape Firmamento_Zephyr_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.27-17.52.43:510][173]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "Firmamento_Zephyr_Terrain",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-14.52.43"
	}
}
[2025.08.27-17.52.43:511][173]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 336
[2025.08.27-17.52.43:511][173]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.52.50:856][196]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.52.50:856][196]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.52.50:856][196]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.52.50:957][196]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.52.50:957][196]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.52.50:957][196]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_procedural_landscape", "params": {"landscape_name": "Abismo_Umbral_Terrain", "layer_index": 2, "size_x": 1009, "size_y": 1009, "scale_x": 100.0, "scale_y": 100.0, "scale_z": 100.0, "location": {"z": 5000.0}, "heightmap_settings": {"octaves": 8, "persistence": 0.6, "noise_type": "ridged", "amplitude": 400, "lacunarity": 3, "frequency": 0.005}}}
[2025.08.27-17.52.50:957][196]LogTemp: Display: UnrealMCPBridge: Executing command: create_procedural_landscape
[2025.08.27-17.52.51:090][196]LogTemp: UnrealMCPLandscapeCommands::HandleCommand - Processing: create_procedural_landscape
[2025.08.27-17.52.51:090][196]LogJson: Warning: Field use_pcg was not found.
[2025.08.27-17.52.51:090][196]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.52.51:090][196]LogJson: Warning: Field enable_nanite was not found.
[2025.08.27-17.52.51:090][196]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.52.51:090][196]LogJson: Warning: Field enable_world_partition was not found.
[2025.08.27-17.52.51:090][196]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-17.52.51:117][196]LogTemp: CreateRobustAuracronLandscape: Created landscape Abismo_Umbral_Terrain_Planicie_Radiante at offset X=0.000 Y=0.000 Z=0.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.52.51:147][196]LogTemp: CreateRobustAuracronLandscape: Created landscape Abismo_Umbral_Terrain_Firmamento_Zephyr at offset X=0.000 Y=0.000 Z=2000.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.52.51:176][196]LogTemp: CreateRobustAuracronLandscape: Created landscape Abismo_Umbral_Terrain_Abismo_Umbral at offset X=0.000 Y=0.000 Z=4000.000 (Size: 2048x2048, Nanite: No)
[2025.08.27-17.52.51:178][196]LogTemp: CreateRobustAuracronLandscape: Created 3 landscape components for Abismo_Umbral_Terrain
[2025.08.27-17.52.51:178][196]LogTemp: HandleCreateProceduralLandscape: Created landscape Abismo_Umbral_Terrain with 3 components (3 layers, PCG: No, Nanite: No)
[2025.08.27-17.52.51:178][196]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_procedural_landscape",
		"landscape_name": "Abismo_Umbral_Terrain",
		"use_pcg": false,
		"enable_nanite": false,
		"enable_world_partition": false,
		"components_created": 3,
		"layers_created": 3,
		"success": true,
		"timestamp": "2025.08.27-14.52.51"
	}
}
[2025.08.27-17.52.51:178][196]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 332
[2025.08.27-17.52.51:178][196]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.52.55:109][209]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.52.55:109][209]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.52.55:109][209]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.52.55:210][209]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.52.55:210][209]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.52.55:210][209]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.27-17.52.55:210][209]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.27-17.52.55:427][209]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: get_actors_in_level
[2025.08.27-17.52.55:428][209]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Planicie_Radiante_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Planicie_Radiante_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Planicie_Radiante_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Firmamento_Zephyr_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Firmamento_Zephyr_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Firmamento_Zephyr_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Abismo_Umbral_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Abismo_Umbral_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Abismo_Umbral_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			}
		]
	}
}
[2025.08.27-17.52.55:428][209]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2794
[2025.08.27-17.52.55:428][209]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.53.38:903][340]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.53.38:903][340]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.53.38:903][340]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.53.39:004][340]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.53.39:004][340]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.53.39:004][340]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_Planicie_Radiante", "layer_index": 0, "material_type": "terrain", "material_properties": {"base_color": [1, 0.9, 0.7, 1], "metallic": 0.1, "roughness": 0.8, "normal_intensity": 1.2, "emissive_color": [0.2, 0.15, 0.05, 1], "opacity": 1}}}
[2025.08.27-17.53.39:004][340]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
[2025.08.27-17.53.39:101][340]LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
[2025.08.27-17.53.39:109][340]LogTemp: CreateRobustMaterial: Created material Material_Planicie_Radiante with Nanite support: Yes
[2025.08.27-17.53.39:109][340]LogTemp: CreateNaniteMaterialInstance: Created instance Material_Planicie_Radiante_Instance for layer 0
[2025.08.27-17.53.39:110][340]LogTemp: HandleCreateLayerMaterials: Created material Material_Planicie_Radiante for layer 0 (Type: terrain, Nanite: Yes)
[2025.08.27-17.53.39:110][340]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_layer_materials",
		"material_name": "Material_Planicie_Radiante",
		"material_type": "terrain",
		"layer_index": 0,
		"nanite_enabled": true,
		"material_layers_enabled": true,
		"success": true,
		"timestamp": "2025.08.27-14.53.39",
		"color_scheme":
		{
			"primary_color": "(R=1.000000,G=0.800000,B=0.200000,A=1.000000)",
			"secondary_color": "(R=0.200000,G=0.800000,B=0.300000,A=1.000000)",
			"accent_color": "(R=1.000000,G=1.000000,B=0.800000,A=1.000000)"
		}
	}
}
[2025.08.27-17.53.39:110][340]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 550
[2025.08.27-17.53.39:110][340]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.53.48:070][367]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.53.48:070][367]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.53.48:070][367]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.53.48:170][368]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.53.48:170][368]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.53.48:170][368]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_Firmamento_Zephyr", "layer_index": 1, "material_type": "terrain", "material_properties": {"opacity": 0.9, "roughness": 0.4, "emissive_color": [0.1, 0.2, 0.3, 1], "base_color": [0.7, 0.8, 1, 1], "metallic": 0.3, "normal_intensity": 0.8}}}
[2025.08.27-17.53.48:170][368]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
[2025.08.27-17.53.48:437][368]LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
[2025.08.27-17.53.48:442][368]LogTemp: CreateRobustMaterial: Created material Material_Firmamento_Zephyr with Nanite support: Yes
[2025.08.27-17.53.48:442][368]LogTemp: CreateNaniteMaterialInstance: Created instance Material_Firmamento_Zephyr_Instance for layer 1
[2025.08.27-17.53.48:442][368]LogTemp: HandleCreateLayerMaterials: Created material Material_Firmamento_Zephyr for layer 1 (Type: terrain, Nanite: Yes)
[2025.08.27-17.53.48:442][368]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_layer_materials",
		"material_name": "Material_Firmamento_Zephyr",
		"material_type": "terrain",
		"layer_index": 1,
		"nanite_enabled": true,
		"material_layers_enabled": true,
		"success": true,
		"timestamp": "2025.08.27-14.53.48",
		"color_scheme":
		{
			"primary_color": "(R=0.200000,G=0.600000,B=1.000000,A=1.000000)",
			"secondary_color": "(R=0.900000,G=0.900000,B=1.000000,A=1.000000)",
			"accent_color": "(R=1.000000,G=1.000000,B=1.000000,A=1.000000)"
		}
	}
}
[2025.08.27-17.53.48:443][368]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 550
[2025.08.27-17.53.48:443][368]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.54.02:938][608]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.02:938][608]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.02:938][608]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.54.03:039][608]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.03:039][608]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.03:039][608]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_materials", "params": {"material_name": "Material_Abismo_Umbral", "layer_index": 2, "material_type": "terrain", "material_properties": {"opacity": 0.8, "roughness": 0.9, "emissive_color": [0.1, 0.05, 0.2, 1], "base_color": [0.2, 0.1, 0.3, 1], "metallic": 0.2, "normal_intensity": 1.5}}}
[2025.08.27-17.54.03:039][608]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_materials
[2025.08.27-17.54.03:209][608]LogTemp: UnrealMCPMaterialCommands::HandleCommand - Command: create_layer_materials
[2025.08.27-17.54.03:217][608]LogTemp: CreateRobustMaterial: Created material Material_Abismo_Umbral with Nanite support: Yes
[2025.08.27-17.54.03:217][608]LogTemp: CreateNaniteMaterialInstance: Created instance Material_Abismo_Umbral_Instance for layer 2
[2025.08.27-17.54.03:217][608]LogTemp: HandleCreateLayerMaterials: Created material Material_Abismo_Umbral for layer 2 (Type: terrain, Nanite: Yes)
[2025.08.27-17.54.03:218][608]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_layer_materials",
		"material_name": "Material_Abismo_Umbral",
		"material_type": "terrain",
		"layer_index": 2,
		"nanite_enabled": true,
		"material_layers_enabled": true,
		"success": true,
		"timestamp": "2025.08.27-14.54.03",
		"color_scheme":
		{
			"primary_color": "(R=0.400000,G=0.100000,B=0.800000,A=1.000000)",
			"secondary_color": "(R=0.100000,G=0.100000,B=0.200000,A=1.000000)",
			"accent_color": "(R=0.800000,G=0.200000,B=1.000000,A=1.000000)"
		}
	}
}
[2025.08.27-17.54.03:218][608]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 546
[2025.08.27-17.54.03:218][608]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.54.16:112][647]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.16:112][647]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.16:112][647]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.54.16:213][648]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.16:213][648]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.16:213][648]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_portal_system", "params": {"map_name": "AURACRON_MainMap", "portals": [{"name": "Portal_Top_River", "source_layer": "Planicie_Radiante", "target_layer": "Firmamento_Zephyr", "source_location": {"x": 0, "y": 6000, "z": 1000}, "target_location": {"x": 0, "y": 6000, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_swirl", "light_beam"], "audio_effects": ["portal_hum", "teleport_sound"]}, {"name": "Portal_Mid_Center", "source_layer": "Planicie_Radiante", "target_layer": "Firmamento_Zephyr", "source_location": {"x": 0, "y": 0, "z": 1000}, "target_location": {"x": 0, "y": 0, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_swirl", "light_beam"], "audio_effects": ["portal_hum", "teleport_sound"]}, {"name": "Portal_Bot_River", "source_layer": "Planicie_Radiante", "target_layer": "Firmamento_Zephyr", "source_location": {"x": 0, "y": -6000, "z": 1000}, "target_location": {"x": 0, "y": -6000, "z": 3000}, "portal_type": "teleporter", "activation_method": "proximity", "activation_distance": 200, "transition_time": 3, "visual_effects": ["energy_swirl", "light_beam"], "audio_effects": ["portal_hum", "teleport_sound"]}]}}
[2025.08.27-17.54.16:213][648]LogTemp: Display: UnrealMCPBridge: Executing command: create_portal_system
[2025.08.27-17.54.16:544][648]LogTemp: [MapSystem] Creating portal system...
[2025.08.27-17.54.16:544][648]LogStreaming: Display: FlushAsyncLoading(477): 1 QueuedPackages, 0 AsyncPackages
[2025.08.27-17.54.16:545][648]LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON_MainMap
[2025.08.27-17.54.16:546][648]LogWorldPartition: ULevel::OnLevelLoaded(AURACRON_MainMap)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.27-17.54.16:546][648]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.27-17.54.16:546][648]LogWorldPartition: UWorldPartition::Initialize : World = /Game/Maps/AURACRON_MainMap.AURACRON_MainMap, World Type = Inactive, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.27-17.54.16:546][648]LogWorldPartition: Warning: Found WorldDataLayers actor 'AURACRON_MainMap_DataLayers' (/Game/Maps/AURACRON_MainMap), but another WorldDataLayers actor 'WorldDataLayers' (/Game/Maps/AURACRON_MainMap) already exists. Actor will be ignored, clean up the invalid actor to remove the warning.
[2025.08.27-17.54.16:546][648]LogWorldPartition: Display: WorldPartition initialize took 413 us (total: 244.399 ms)
[2025.08.27-17.54.16:549][648]LogTemp: [MapSystem] Portal created: Portal_Top_River (Planicie_Radiante -> Firmamento_Zephyr) Type: teleporter, Activation: proximity
[2025.08.27-17.54.16:549][648]LogTemp: [MapSystem] Portal created: Portal_Mid_Center (Planicie_Radiante -> Firmamento_Zephyr) Type: teleporter, Activation: proximity
[2025.08.27-17.54.16:550][648]LogTemp: [MapSystem] Portal created: Portal_Bot_River (Planicie_Radiante -> Firmamento_Zephyr) Type: teleporter, Activation: proximity
[2025.08.27-17.54.16:550][648]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.27-17.54.16:625][648]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/AURACRON_MainMap" FILE="../../../../../../Game/AURACRON/Content/Maps/AURACRON_MainMap.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.27-17.54.16:646][648]LogUObjectHash: Compacting FUObjectHashTables data took   0.62ms
[2025.08.27-17.54.16:662][648]LogSavePackage: Moving output files for package: /Game/Maps/AURACRON_MainMap
[2025.08.27-17.54.16:662][648]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_MainMap0536C17841347A42FF08AE96A89AD545.tmp' to '../../../../../../Game/AURACRON/Content/Maps/AURACRON_MainMap.umap'
[2025.08.27-17.54.16:671][648]LogFileHelpers: Saving map 'AURACRON_MainMap' took 0.063
[2025.08.27-17.54.16:687][648]LogFileHelpers: InternalPromptForCheckoutAndSave took 136.786 ms
[2025.08.27-17.54.16:687][648]LogTemp: [MapSystem] Portal system created successfully with 3 portals
[2025.08.27-17.54.16:687][648]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_portal_system",
		"map_name": "AURACRON_MainMap",
		"portals_created": [
			{
				"name": "Portal_Top_River",
				"source_layer": "Planicie_Radiante",
				"target_layer": "Firmamento_Zephyr",
				"portal_type": "teleporter",
				"activation_method": "proximity",
				"activation_distance": 200,
				"transition_time": 3,
				"created": true,
				"actor_name": "Portal_Top_River",
				"components_added": 4,
				"final_location":
				{
					"x": 0,
					"y": 6000,
					"z": 1000
				}
			},
			{
				"name": "Portal_Mid_Center",
				"source_layer": "Planicie_Radiante",
				"target_layer": "Firmamento_Zephyr",
				"portal_type": "teleporter",
				"activation_method": "proximity",
				"activation_distance": 200,
				"transition_time": 3,
				"created": true,
				"actor_name": "Portal_Mid_Center",
				"components_added": 4,
				"final_location":
				{
					"x": 0,
					"y": 0,
					"z": 1000
				}
			},
			{
				"name": "Portal_Bot_River",
				"source_layer": "Planicie_Radiante",
				"target_layer": "Firmamento_Zephyr",
				"portal_type": "teleporter",
				"activation_method": "proximity",
				"activation_distance": 200,
				"transition_time": 3,
				"created": true,
				"actor_name": "Portal_Bot_River",
				"components_added": 4,
				"final_location":
				{
					"x": 0,
					"y": -6000,
					"z": 1000
				}
			}
		],
		"total_portals": 3,
		"success": true
	}
}
[2025.08.27-17.54.16:687][648]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1499
[2025.08.27-17.54.16:687][648]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.54.16:743][649]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.27-17.54.16:744][649]LogContentValidation: Enabled validators:
[2025.08.27-17.54.16:744][649]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.27-17.54.16:744][649]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.27-17.54.16:744][649]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.27-17.54.16:744][649]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.27-17.54.16:744][649]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.27-17.54.16:744][649]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.27-17.54.16:744][649]AssetCheck: /Game/Maps/AURACRON_MainMap Validando ativo
[2025.08.27-17.54.27:162][681]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.27:162][681]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.27:162][681]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.54.27:263][681]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.27:263][681]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.27:263][681]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_elevator_system", "params": {"map_name": "AURACRON_MainMap", "elevator_locations": [{"y": 7500.0, "z": 0.0}, {"y": -7500.0, "z": 0.0}, {"x": 7500.0, "z": 0.0}, {"x": -7500.0, "z": 0.0}], "capacity": 5, "travel_time": 2.5, "vulnerability_enabled": true}}
[2025.08.27-17.54.27:263][681]LogTemp: Display: UnrealMCPBridge: Executing command: create_elevator_system
[2025.08.27-17.54.27:363][681]LogTemp: [MapSystem] Creating elevator system...
[2025.08.27-17.54.27:364][681]LogTemp: [MapSystem] Elevator created: Elevator_0 (Capacity: 5, Travel Time: 2.5s, Vulnerable: Yes, Components: 5)
[2025.08.27-17.54.27:364][681]LogTemp: [MapSystem] Elevator created: Elevator_1 (Capacity: 5, Travel Time: 2.5s, Vulnerable: Yes, Components: 5)
[2025.08.27-17.54.27:364][681]LogTemp: [MapSystem] Elevator created: Elevator_2 (Capacity: 5, Travel Time: 2.5s, Vulnerable: Yes, Components: 5)
[2025.08.27-17.54.27:364][681]LogTemp: [MapSystem] Elevator created: Elevator_3 (Capacity: 5, Travel Time: 2.5s, Vulnerable: Yes, Components: 5)
[2025.08.27-17.54.27:365][681]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.27-17.54.27:429][681]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/AURACRON_MainMap" FILE="../../../../../../Game/AURACRON/Content/Maps/AURACRON_MainMap.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.27-17.54.27:446][681]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.08.27-17.54.27:473][681]LogSavePackage: Moving output files for package: /Game/Maps/AURACRON_MainMap
[2025.08.27-17.54.27:474][681]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_MainMap0A9DBA1D461D6D3DAD40B3BB24143B79.tmp' to '../../../../../../Game/AURACRON/Content/Maps/AURACRON_MainMap.umap'
[2025.08.27-17.54.27:488][681]LogFileHelpers: Saving map 'AURACRON_MainMap' took 0.071
[2025.08.27-17.54.27:507][681]LogFileHelpers: InternalPromptForCheckoutAndSave took 141.816 ms (total: 278.602 ms)
[2025.08.27-17.54.27:507][681]LogTemp: [MapSystem] Elevator system created successfully with 4 elevators
[2025.08.27-17.54.27:507][681]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_elevator_system",
		"map_name": "AURACRON_MainMap",
		"elevators_created": [
			{
				"name": "Elevator_0",
				"capacity": 5,
				"travel_time": 2.5,
				"vulnerability_enabled": true,
				"created": true,
				"actor_name": "Elevator_0",
				"components_added": 5,
				"location":
				{
					"x": 0,
					"y": 0,
					"z": 0
				},
				"component_specifications":
				{
					"platform": "Cube mesh with dynamic material",
					"collision": "Box collision for overlap detection",
					"shaft": "Cylinder mesh for visual shaft",
					"audio": "Audio component for elevator sounds"
				}
			},
			{
				"name": "Elevator_1",
				"capacity": 5,
				"travel_time": 2.5,
				"vulnerability_enabled": true,
				"created": true,
				"actor_name": "Elevator_1",
				"components_added": 5,
				"location":
				{
					"x": 0,
					"y": 0,
					"z": 0
				},
				"component_specifications":
				{
					"platform": "Cube mesh with dynamic material",
					"collision": "Box collision for overlap detection",
					"shaft": "Cylinder mesh for visual shaft",
					"audio": "Audio component for elevator sounds"
				}
			},
			{
				"name": "Elevator_2",
				"capacity": 5,
				"travel_time": 2.5,
				"vulnerability_enabled": true,
				"created": true,
				"actor_name": "Elevator_2",
				"components_added": 5,
				"location":
				{
					"x": 0,
					"y": 0,
					"z": 0
				},
				"component_specifications":
				{
					"platform": "Cube mesh with dynamic material",
					"collision": "Box collision for overlap detection",
					"shaft": "Cylinder mesh for visual shaft",
					"audio": "Audio component for elevator sounds"
				}
			},
			{
				"name": "Elevator_3",
				"capacity": 5,
				"travel_time": 2.5,
				"vulnerability_enabled": true,
				"created": true,
				"actor_name": "Elevator_3",
				"components_added": 5,
				"location":
				{
					"x": 0,
					"y": 0,
					"z": 0
				},
				"component_specifications":
				{
					"platform": "Cube mesh with dynamic material",
					"collision": "Box collision for overlap detection",
					"shaft": "Cylinder mesh for visual shaft",
					"audio": "Audio component for elevator sounds"
				}
			}
		],
		"total_elevators": 4,
		"default_capacity": 5,
		"default_travel_time": 2.5,
		"vulnerability_enabled": true,
		"success": true
	}
}
[2025.08.27-17.54.27:508][681]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2436
[2025.08.27-17.54.27:508][681]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.54.27:751][682]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.27-17.54.27:751][682]LogContentValidation: Enabled validators:
[2025.08.27-17.54.27:751][682]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.27-17.54.27:751][682]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.27-17.54.27:751][682]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.27-17.54.27:751][682]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.27-17.54.27:751][682]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.27-17.54.27:751][682]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.27-17.54.27:751][682]AssetCheck: /Game/Maps/AURACRON_MainMap Validando ativo
[2025.08.27-17.54.42:939][728]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.42:939][728]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.42:939][728]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.54.43:040][729]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.54.43:040][729]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.54.43:040][729]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_tower_system", "params": {"tower_system_name": "AURACRON_Tower_System", "layer_configurations": [{"layer_name": "Planicie_Radiante", "tower_count": 11, "tower_positions": [{"x": -6000, "y": 4000, "z": 1000}, {"x": -4000, "y": 3000, "z": 1000}, {"x": -2000, "y": 2000, "z": 1000}, {"x": 0, "y": 1000, "z": 1000}, {"x": 2000, "y": 2000, "z": 1000}, {"x": 4000, "y": 3000, "z": 1000}, {"x": 6000, "y": 4000, "z": 1000}, {"x": -6000, "y": -4000, "z": 1000}, {"x": -4000, "y": -3000, "z": 1000}, {"x": 4000, "y": -3000, "z": 1000}, {"x": 6000, "y": -4000, "z": 1000}]}, {"layer_name": "Firmamento_Zephyr", "tower_count": 8, "tower_positions": [{"x": -5000, "y": 3000, "z": 3000}, {"x": -2500, "y": 1500, "z": 3000}, {"x": 2500, "y": 1500, "z": 3000}, {"x": 5000, "y": 3000, "z": 3000}, {"x": -5000, "y": -3000, "z": 3000}, {"x": -2500, "y": -1500, "z": 3000}, {"x": 2500, "y": -1500, "z": 3000}, {"x": 5000, "y": -3000, "z": 3000}]}, {"layer_name": "Abismo_Umbral", "tower_count": 6, "tower_positions": [{"x": -4000, "y": 2000, "z": 5000}, {"x": 0, "y": 0, "z": 5000}, {"x": 4000, "y": 2000, "z": 5000}, {"x": -4000, "y": -2000, "z": 5000}, {"x": 0, "y": 0, "z": 5000}, {"x": 4000, "y": -2000, "z": 5000}]}], "tower_types": ["basic", "advanced", "nexus"], "damage_scaling": {"Planicie_Radiante": 1.0, "Firmamento_Zephyr": 1.2, "Abismo_Umbral": 1.5}, "range_modifiers": {"Planicie_Radiante": 800.0, "Firmamento_Zephyr": 900.0, "Abismo_Umbral": 1000.0}}}
[2025.08.27-17.54.43:040][729]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_tower_system
[2025.08.27-17.54.43:367][729]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_multilayer_tower_system
[2025.08.27-17.54.43:367][729]LogJson: Warning: Field towers_per_lane was not found.
[2025.08.27-17.54.43:367][729]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-17.54.43:367][729]LogJson: Warning: Field towers_per_lane was not found.
[2025.08.27-17.54.43:367][729]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-17.54.43:367][729]LogJson: Warning: Field towers_per_lane was not found.
[2025.08.27-17.54.43:367][729]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-17.54.43:367][729]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/MOBA/TowerSystems/AURACRON_Tower_System.AURACRON_Tower_System' could not be found in the Asset Registry.
[2025.08.27-17.54.43:374][729]LogTemp: Multilayer Tower System created: AURACRON_Tower_System (Towers: 0, Layers: 3, Saved: No)
[2025.08.27-17.54.43:374][729]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"tower_system_name": "AURACRON_Tower_System",
		"package_path": "/Game/Auracron/MOBA/TowerSystems/AURACRON_Tower_System",
		"towers_created": 0,
		"layers_configured": 3,
		"saved_to_disk": false,
		"config_path": "../../../../../../Game/AURACRON/Content/Auracron/MOBA/TowerSystems/AURACRON_Tower_System_Config.json",
		"configuration":
		{
			"tower_system_name": "AURACRON_Tower_System",
			"layer_configurations": [
				{
					"layer_name": "Planicie_Radiante",
					"tower_count": 11,
					"tower_positions": [
						{
							"x": -6000,
							"y": 4000,
							"z": 1000
						},
						{
							"x": -4000,
							"y": 3000,
							"z": 1000
						},
						{
							"x": -2000,
							"y": 2000,
							"z": 1000
						},
						{
							"x": 0,
							"y": 1000,
							"z": 1000
						},
						{
							"x": 2000,
							"y": 2000,
							"z": 1000
						},
						{
							"x": 4000,
							"y": 3000,
							"z": 1000
						},
						{
							"x": 6000,
							"y": 4000,
							"z": 1000
						},
						{
							"x": -6000,
							"y": -4000,
							"z": 1000
						},
						{
							"x": -4000,
							"y": -3000,
							"z": 1000
						},
						{
							"x": 4000,
							"y": -3000,
							"z": 1000
						},
						{
							"x": 6000,
							"y": -4000,
							"z": 1000
						}
					]
				},
				{
					"layer_name": "Firmamento_Zephyr",
					"tower_count": 8,
					"tower_positions": [
						{
							"x": -5000,
							"y": 3000,
							"z": 3000
						},
						{
							"x": -2500,
							"y": 1500,
							"z": 3000
						},
						{
							"x": 2500,
							"y": 1500,
							"z": 3000
						},
						{
							"x": 5000,
							"y": 3000,
							"z": 3000
						},
						{
							"x": -5000,
							"y": -3000,
							"z": 3000
						},
						{
							"x": -2500,
							"y": -1500,
							"z": 3000
						},
						{
							"x": 2500,
							"y": -1500,
							"z": 3000
						},
						{
							"x": 5000,
							"y": -3000,
							"z": 3000
						}
					]
				},
				{
					"layer_name": "Abismo_Umbral",
					"tower_count": 6,
					"tower_positions": [
						{
							"x": -4000,
							"y": 2000,
							"z": 5000
						},
						{
							"x": 0,
							"y": 0,
							"z": 5000
						},
						{
							"x": 4000,
							"y": 2000,
							"z": 5000
						},
						{
							"x": -4000,
							"y": -2000,
							"z": 5000
						},
						{
							"x": 0,
							"y": 0,
							"z": 5000
						},
						{
							"x": 4000,
							"y": -2000,
							"z": 5000
						}
					]
				}
			],
			"towers_created": 0
		}
	}
}
[2025.08.27-17.54.43:375][729]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 2733
[2025.08.27-17.54.43:375][729]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.55.23:353][849]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.55.23:353][849]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.55.23:353][849]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.55.23:455][850]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.55.23:455][850]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.55.23:455][850]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_epic_objectives", "params": {"epic_system_name": "AURACRON_Epic_Objectives", "epic_objectives": [{"name": "Guardiao_da_Aurora", "layer": "Planicie_Radiante", "location": {"z": 1000}, "buffs": ["+20% dano", "regeneracao de mana"], "respawn_time": 300, "health": 5000}, {"name": "Senhor_dos_Ventos", "layer": "Firmamento_Zephyr", "location": {"z": 3000}, "buffs": ["+30% velocidade de movimento", "atravessar terreno"], "respawn_time": 240, "health": 4500}, {"name": "Arqui_Sombra", "layer": "Abismo_Umbral", "location": {"z": 5000}, "buffs": ["invisibilidade de equipe", "dano verdadeiro"], "respawn_time": 420, "health": 6000}], "spawn_conditions": {"min_game_time": 600, "required_structures_destroyed": 2}, "multilayer_effects": {"cross_layer_vision": true, "global_buffs": true, "cascade_effects": true}}}
[2025.08.27-17.55.23:455][850]LogTemp: Display: UnrealMCPBridge: Executing command: create_epic_objectives
[2025.08.27-17.55.23:711][850]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_epic_objectives
[2025.08.27-17.55.23:711][850]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameter: epic_objectives_system_name"
}
[2025.08.27-17.55.23:711][850]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 94
[2025.08.27-17.55.23:711][850]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.55.32:583][954]LogFactory: FactoryCreateFile: DataTable with ReimportDataTableFactory (0 1 C:/Game/AURACRON/Content/Auracron/MOBA/TowerSystems/AURACRON_Tower_System_Config.json)
[2025.08.27-17.55.32:711][954]LogSlate: Took 0.000199 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.27-17.55.37:351][954]LogSlate: Window 'Opções da Tabela de Dados' being destroyed
[2025.08.27-17.55.37:393][954]LogUObjectHash: Compacting FUObjectHashTables data took   0.53ms
[2025.08.27-17.55.37:410][954]LogUObjectHash: Compacting FUObjectHashTables data took   0.50ms
[2025.08.27-17.55.37:419][954]LogAutomationController: Ignoring very large delta of 5.28 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.08.27-17.55.40:439][ 22]AssetReimport: Importar novo ativo /Game/Auracron/MOBA/TowerSystems/AURACRON_Tower_System_Config.
[2025.08.27-17.55.40:439][ 22]AssetReimport: Falha ao importar arquivo C:/Game/AURACRON/Content/Auracron/MOBA/TowerSystems/AURACRON_Tower_System_Config.json.
[2025.08.27-17.57.05:481][811]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.57.05:481][811]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.57.05:481][811]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.57.05:583][811]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.57.05:583][811]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.57.05:583][811]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_epic_objectives", "params": {"epic_system_name": "AURACRON_Epic_Objectives", "epic_objectives": [{"name": "Guardiao_da_Aurora", "health": 5000, "location": {"z": 1000}, "buffs": ["+20% dano", "regeneracao de mana"], "layer": "Planicie_Radiante", "respawn_time": 300}, {"name": "Senhor_dos_Ventos", "health": 4500, "location": {"z": 3000}, "buffs": ["+30% velocidade de movimento", "atravessar terreno"], "layer": "Firmamento_Zephyr", "respawn_time": 240}, {"name": "Arqui_Sombra", "health": 6000, "location": {"z": 5000}, "buffs": ["invisibilidade de equipe", "dano verdadeiro"], "layer": "Abismo_Umbral", "respawn_time": 420}], "spawn_conditions": {"min_game_time": 600, "required_structures_destroyed": 2}, "multilayer_effects": {"cascade_effects": true, "cross_layer_vision": true, "global_buffs": true}}}
[2025.08.27-17.57.05:583][811]LogTemp: Display: UnrealMCPBridge: Executing command: create_epic_objectives
[2025.08.27-17.57.05:697][811]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_epic_objectives
[2025.08.27-17.57.05:697][811]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameter: epic_objectives_system_name"
}
[2025.08.27-17.57.05:698][811]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 94
[2025.08.27-17.57.05:698][811]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-17.59.13:062][194]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.59.13:062][194]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.59.13:062][194]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-17.59.13:163][194]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-17.59.13:163][194]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-17.59.13:163][194]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_epic_objectives", "params": {"epic_system_name": "AURACRON_Epic_Objectives", "epic_objectives": [{"name": "Guardiao_da_Aurora", "health": 5000, "location": {"z": 1000}, "buffs": ["+20% dano", "regeneracao de mana"], "layer": "Planicie_Radiante", "respawn_time": 300}, {"name": "Senhor_dos_Ventos", "health": 4500, "location": {"z": 3000}, "buffs": ["+30% velocidade de movimento", "atravessar terreno"], "layer": "Firmamento_Zephyr", "respawn_time": 240}, {"name": "Arqui_Sombra", "health": 6000, "location": {"z": 5000}, "buffs": ["invisibilidade de equipe", "dano verdadeiro"], "layer": "Abismo_Umbral", "respawn_time": 420}], "spawn_conditions": {"min_game_time": 600, "required_structures_destroyed": 2}, "multilayer_effects": {"cascade_effects": true, "cross_layer_vision": true, "global_buffs": true}}}
[2025.08.27-17.59.13:163][194]LogTemp: Display: UnrealMCPBridge: Executing command: create_epic_objectives
[2025.08.27-17.59.13:387][194]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_epic_objectives
[2025.08.27-17.59.13:387][194]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameter: epic_objectives_system_name"
}
[2025.08.27-17.59.13:387][194]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 94
[2025.08.27-17.59.13:387][194]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.02.31:959][790]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.02.31:959][790]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.02.31:959][790]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.02.32:060][790]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.02.32:060][790]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.02.32:060][790]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_pathfinding", "params": {"system_name": "AURACRON_Pathfinding_System", "layer_count": 3, "layer_heights": [1000.0, 3000.0, 5000.0], "transition_costs": {"portal": 2.0, "elevator": 3.0, "bridge": 1.5, "emergency_exit": 4.0}, "heuristic_settings": {"vertical_preference": 1.2, "layer_penalty": 0.5, "emergency_cost_multiplier": 2}}}
[2025.08.27-18.02.32:060][790]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_pathfinding
[2025.08.27-18.02.32:077][790]LogTemp: Multilayer Pathfinding: Set transition cost portal = 2.000000
[2025.08.27-18.02.32:077][790]LogTemp: Multilayer Pathfinding: Set transition cost elevator = 3.000000
[2025.08.27-18.02.32:077][790]LogTemp: Multilayer Pathfinding: Set transition cost bridge = 1.500000
[2025.08.27-18.02.32:077][790]LogTemp: Multilayer Pathfinding: Set transition cost emergency_exit = 4.000000
[2025.08.27-18.02.32:077][790]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Pathfinding/AURACRON_Pathfinding_System.AURACRON_Pathfinding_System' could not be found in the Asset Registry.
[2025.08.27-18.02.32:077][790]LogTemp: Multilayer Pathfinding system created and saved: /Game/Auracron/Pathfinding/AURACRON_Pathfinding_System (Layers: 3, Saved: No)
[2025.08.27-18.02.32:077][790]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"system_name": "AURACRON_Pathfinding_System",
		"package_path": "/Game/Auracron/Pathfinding/AURACRON_Pathfinding_System",
		"layer_count": 3,
		"saved_to_disk": false,
		"full_path": "../../../../../../Game/AURACRON/Content/Auracron/Pathfinding/AURACRON_Pathfinding_System.uasset",
		"layer_configuration": [
			{
				"layer_index": 0,
				"height": 1000,
				"navigation_data_available": false
			},
			{
				"layer_index": 1,
				"height": 3000,
				"navigation_data_available": false
			},
			{
				"layer_index": 2,
				"height": 5000,
				"navigation_data_available": false
			}
		],
		"transition_costs":
		{
			"portal": 2,
			"elevator": 3,
			"bridge": 1.5,
			"emergency_exit": 4
		}
	}
}
[2025.08.27-18.02.32:077][790]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 772
[2025.08.27-18.02.32:077][790]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.02.40:836][817]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.02.40:836][817]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.02.40:836][817]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.02.40:937][817]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.02.40:937][817]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.02.40:937][817]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_fog_of_war", "params": {"system_name": "AURACRON_Fog_System", "update_frequency": 0.1, "performance_mode": "medium", "layer_configs": [{"layer_name": "Planicie_Radiante", "fog_density": 0.3, "visibility_range": 1200}, {"layer_name": "Firmamento_Zephyr", "fog_density": 0.5, "visibility_range": 1000}, {"layer_name": "Abismo_Umbral", "fog_density": 0.8, "visibility_range": 800}], "visibility_ranges": {"same_layer": 1200.0, "vertical_adjacent": 400.0, "ward_same_layer": 1600.0, "ward_vertical": 600.0}}}
[2025.08.27-18.02.40:937][817]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_fog_of_war
[2025.08.27-18.02.41:082][817]LogTemp: Created fog parameter collection: AURACRON_Fog_System_FogParams with 3 layers
[2025.08.27-18.02.41:083][817]LogTemp: Multilayer Fog of War: Created fog actor for layer 0 at height 1000.000000
[2025.08.27-18.02.41:083][817]LogTemp: Multilayer Fog of War: Created fog actor for layer 1 at height 3000.000000
[2025.08.27-18.02.41:083][817]LogTemp: Multilayer Fog of War: Created fog actor for layer 2 at height 5000.000000
[2025.08.27-18.02.41:083][817]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System.AURACRON_Fog_System' could not be found in the Asset Registry.
[2025.08.27-18.02.41:083][817]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.27-18.02.41:118][817]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System_FogParams] ([1] browsable assets)...
[2025.08.27-18.02.41:118][817]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System_FogParams]
[2025.08.27-18.02.41:128][817]LogSavePackage: Moving output files for package: /Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System_FogParams
[2025.08.27-18.02.41:128][817]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Fog_System_FogParamsCB337AD3497DE5C956310792D5AF1C2D.tmp' to '../../../../../../Game/AURACRON/Content/Auracron/Vision/FogOfWar/AURACRON_Fog_System_FogParams.uasset'
[2025.08.27-18.02.41:145][817]LogFileHelpers: InternalPromptForCheckoutAndSave took 61.324 ms (total: 339.927 ms)
[2025.08.27-18.02.41:145][817]LogTemp: Multilayer Fog of War system created: AURACRON_Fog_System (Layers: 3, Performance: medium, Saved: No)
[2025.08.27-18.02.41:145][817]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"system_name": "AURACRON_Fog_System",
		"package_path": "/Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System",
		"parameter_collection_path": "/Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System_FogParams",
		"layer_count": 3,
		"performance_mode": "medium",
		"update_frequency": 0.10000000149011612,
		"saved_to_disk": false,
		"full_path": "../../../../../../Game/AURACRON/Content/Auracron/Vision/FogOfWar/AURACRON_Fog_System.uasset",
		"fog_actors": [
			{
				"layer_index": 0,
				"actor_name": "FogOfWar_Layer_0_AURACRON_Fog_System",
				"height": 1000
			},
			{
				"layer_index": 1,
				"actor_name": "FogOfWar_Layer_1_AURACRON_Fog_System",
				"height": 3000
			},
			{
				"layer_index": 2,
				"actor_name": "FogOfWar_Layer_2_AURACRON_Fog_System",
				"height": 5000
			}
		]
	}
}
[2025.08.27-18.02.41:145][817]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 861
[2025.08.27-18.02.41:145][817]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.02.41:158][817]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.27-18.02.41:468][818]LogContentValidation: Display: Starting to validate 1 assets
[2025.08.27-18.02.41:468][818]LogContentValidation: Enabled validators:
[2025.08.27-18.02.41:468][818]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.27-18.02.41:468][818]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.27-18.02.41:468][818]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.27-18.02.41:468][818]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.27-18.02.41:468][818]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.27-18.02.41:468][818]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.27-18.02.41:468][818]AssetCheck: /Game/Auracron/Vision/FogOfWar/AURACRON_Fog_System_FogParams Validando ativo
[2025.08.27-18.02.56:247][863]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.02.56:247][863]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.02.56:247][863]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.02.56:348][863]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.02.56:348][863]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.02.56:348][863]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_layer_collision_profiles", "params": {"profile_system_name": "AURACRON_Collision_System", "layer_profiles": [{"collision_channels": ["RadianteLayer", "VerticalConnector"], "layer_name": "Planicie_Radiante", "collision_responses": {"RadianteLayer": "Block", "ZephyrLayer": "Ignore", "UmbralLayer": "Ignore", "VerticalConnector": "Overlap"}}, {"collision_channels": ["ZephyrLayer", "VerticalConnector"], "layer_name": "Firmamento_Zephyr", "collision_responses": {"RadianteLayer": "Ignore", "ZephyrLayer": "Block", "UmbralLayer": "Ignore", "VerticalConnector": "Overlap"}}, {"collision_channels": ["UmbralLayer", "VerticalConnector"], "layer_name": "Abismo_Umbral", "collision_responses": {"RadianteLayer": "Ignore", "ZephyrLayer": "Ignore", "UmbralLayer": "Block", "VerticalConnector": "Overlap"}}], "chaos_settings": {"solver_iterations": 8, "collision_margin": 0.1, "sleep_threshold": 0.05}}}
[2025.08.27-18.02.56:348][863]LogTemp: Display: UnrealMCPBridge: Executing command: create_layer_collision_profiles
[2025.08.27-18.02.56:415][863]LogTemp: FUnrealMCPCollisionCommands::HandleCommand - Processing: create_layer_collision_profiles
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field profile_name was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field profile_name was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.02.56:415][863]LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer0_Profile for layer 0 (Planicie_Radiante) with collision enabled: 3
[2025.08.27-18.02.56:415][863]LogTemp: Layer Collision Profiles: Created profile Layer0_Profile for layer 0 (Planicie_Radiante)
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field profile_name was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field profile_name was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.02.56:415][863]LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer1_Profile for layer 1 (Firmamento_Zephyr) with collision enabled: 3
[2025.08.27-18.02.56:415][863]LogTemp: Layer Collision Profiles: Created profile Layer1_Profile for layer 1 (Firmamento_Zephyr)
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field profile_name was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field profile_name was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.02.56:415][863]LogTemp: CreateLayerCollisionProfile: REAL collision profile created Layer2_Profile for layer 2 (Abismo_Umbral) with collision enabled: 3
[2025.08.27-18.02.56:415][863]LogTemp: Layer Collision Profiles: Created profile Layer2_Profile for layer 2 (Abismo_Umbral)
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field enable_ccd was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-18.02.56:415][863]LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 0 (Iterations: 10.000000, Margin: 0.050000, CCD: No)
[2025.08.27-18.02.56:415][863]LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 0
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field enable_ccd was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-18.02.56:415][863]LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 1 (Iterations: 6.000000, Margin: 0.200000, CCD: No)
[2025.08.27-18.02.56:415][863]LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 1
[2025.08.27-18.02.56:415][863]LogJson: Warning: Field enable_ccd was not found.
[2025.08.27-18.02.56:415][863]LogJson: Warning: Json Value of type 'Null' used as a 'Boolean'.
[2025.08.27-18.02.56:415][863]LogTemp: ConfigureChaosLayerSolver: REAL Chaos solver configured for layer 2 (Iterations: 12.000000, Margin: 0.030000, CCD: Yes)
[2025.08.27-18.02.56:415][863]LogTemp: Layer Collision Profiles: Configured Chaos Physics for layer 2
[2025.08.27-18.02.56:415][863]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/Collision/Profiles/AURACRON_Collision_System.AURACRON_Collision_System' could not be found in the Asset Registry.
[2025.08.27-18.02.56:416][863]LogTemp: Layer Collision Profiles system created: AURACRON_Collision_System (Profiles: 3, Layers: 3, Saved: No)
[2025.08.27-18.02.56:417][863]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"profile_system_name": "AURACRON_Collision_System",
		"package_path": "/Game/Auracron/Collision/Profiles/AURACRON_Collision_System",
		"profiles_created": 3,
		"layers_configured": 3,
		"saved_to_disk": false,
		"config_path": "../../../../../../Game/AURACRON/Content/Auracron/Collision/Profiles/AURACRON_Collision_System_Config.json",
		"configuration":
		{
			"profile_system_name": "AURACRON_Collision_System",
			"layer_profiles": [
				{
					"collision_channels": [
						"RadianteLayer",
						"VerticalConnector"
					],
					"layer_name": "Planicie_Radiante",
					"collision_responses":
					{
						"RadianteLayer": "Block",
						"ZephyrLayer": "Ignore",
						"UmbralLayer": "Ignore",
						"VerticalConnector": "Overlap"
					}
				},
				{
					"collision_channels": [
						"ZephyrLayer",
						"VerticalConnector"
					],
					"layer_name": "Firmamento_Zephyr",
					"collision_responses":
					{
						"RadianteLayer": "Ignore",
						"ZephyrLayer": "Block",
						"UmbralLayer": "Ignore",
						"VerticalConnector": "Overlap"
					}
				},
				{
					"collision_channels": [
						"UmbralLayer",
						"VerticalConnector"
					],
					"layer_name": "Abismo_Umbral",
					"collision_responses":
					{
						"RadianteLayer": "Ignore",
						"ZephyrLayer": "Ignore",
						"UmbralLayer": "Block",
						"VerticalConnector": "Overlap"
					}
				}
			],
			"profiles_created": 3
		},
		"created_profiles": [
			{
				"layer_index": 0,
				"profile_name": "Layer0_Profile"
			},
			{
				"layer_index": 1,
				"profile_name": "Layer1_Profile"
			},
			{
				"layer_index": 2,
				"profile_name": "Layer2_Profile"
			}
		]
	}
}
[2025.08.27-18.02.56:417][863]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1755
[2025.08.27-18.02.56:417][863]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.03.13:441][915]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.13:441][915]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.13:441][915]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.03.13:541][915]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.13:541][915]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.13:541][915]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Sol_Planicie_Radiante", "light_type": "directional", "location": {"z": 2000.0}, "layer_index": 0, "intensity": 3, "color": {"a": 1, "r": 1, "b": 0.7, "g": 0.9}, "attenuation_radius": 10000, "use_lumen": true, "cast_shadows": true}}
[2025.08.27-18.03.13:541][915]LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
[2025.08.27-18.03.13:752][915]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
[2025.08.27-18.03.13:752][915]LogJson: Warning: Field x was not found.
[2025.08.27-18.03.13:752][915]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.03.13:752][915]LogJson: Warning: Field y was not found.
[2025.08.27-18.03.13:752][915]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.03.13:753][915]LogTemp: CreateRobustLighting: Created light Sol_Planicie_Radiante (Type: directional, Intensity: 3.0, Lumen: Yes, Shadows: Yes)
[2025.08.27-18.03.13:753][915]LogTemp: HandleCreateDynamicLighting: Created light Sol_Planicie_Radiante (Type: directional, Layer: 0, Intensity: 3.0, Success: Yes)
[2025.08.27-18.03.13:753][915]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_dynamic_lighting",
		"light_name": "Sol_Planicie_Radiante",
		"light_type": "directional",
		"layer_index": 0,
		"intensity": 3,
		"use_lumen": true,
		"cast_shadows": true,
		"success": true,
		"timestamp": "2025.08.27-15.03.13",
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 2000
		},
		"color":
		{
			"r": 1,
			"g": 0.89999997615814209,
			"b": 0.69999998807907104,
			"a": 1
		}
	}
}
[2025.08.27-18.03.13:753][915]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 477
[2025.08.27-18.03.13:753][915]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.03.22:712][942]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.22:712][942]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.22:712][942]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.03.22:813][943]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.22:813][943]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.22:813][943]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Ventos_Firmamento_Zephyr", "light_type": "directional", "location": {"z": 4000.0}, "layer_index": 1, "intensity": 2.5, "color": {"a": 1, "r": 0.7, "b": 1, "g": 0.8}, "attenuation_radius": 8000, "use_lumen": true, "cast_shadows": true}}
[2025.08.27-18.03.22:813][943]LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
[2025.08.27-18.03.23:093][943]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
[2025.08.27-18.03.23:093][943]LogJson: Warning: Field x was not found.
[2025.08.27-18.03.23:093][943]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.03.23:093][943]LogJson: Warning: Field y was not found.
[2025.08.27-18.03.23:093][943]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.03.23:093][943]LogTemp: CreateRobustLighting: Created light Ventos_Firmamento_Zephyr (Type: directional, Intensity: 2.5, Lumen: Yes, Shadows: Yes)
[2025.08.27-18.03.23:093][943]LogTemp: HandleCreateDynamicLighting: Created light Ventos_Firmamento_Zephyr (Type: directional, Layer: 1, Intensity: 2.5, Success: Yes)
[2025.08.27-18.03.23:093][943]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_dynamic_lighting",
		"light_name": "Ventos_Firmamento_Zephyr",
		"light_type": "directional",
		"layer_index": 1,
		"intensity": 2.5,
		"use_lumen": true,
		"cast_shadows": true,
		"success": true,
		"timestamp": "2025.08.27-15.03.23",
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 4000
		},
		"color":
		{
			"r": 0.69999998807907104,
			"g": 0.80000001192092896,
			"b": 1,
			"a": 1
		}
	}
}
[2025.08.27-18.03.23:094][943]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 482
[2025.08.27-18.03.23:094][943]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.03.26:121][954]LogSlate: Took 0.000223 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.27-18.03.43:320][287]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.43:320][287]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.43:320][287]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.03.43:421][288]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.43:421][288]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.43:421][288]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_dynamic_lighting", "params": {"light_name": "Sombras_Abismo_Umbral", "light_type": "directional", "location": {"z": 6000.0}, "layer_index": 2, "intensity": 1.5, "color": {"a": 1, "r": 0.4, "b": 0.8, "g": 0.2}, "attenuation_radius": 6000, "use_lumen": true, "cast_shadows": true}}
[2025.08.27-18.03.43:421][288]LogTemp: Display: UnrealMCPBridge: Executing command: create_dynamic_lighting
[2025.08.27-18.03.43:729][288]LogTemp: UnrealMCPVisualEffectsCommands::HandleCommand - Command: create_dynamic_lighting
[2025.08.27-18.03.43:729][288]LogJson: Warning: Field x was not found.
[2025.08.27-18.03.43:729][288]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.03.43:729][288]LogJson: Warning: Field y was not found.
[2025.08.27-18.03.43:729][288]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.03.43:729][288]LogTemp: CreateRobustLighting: Created light Sombras_Abismo_Umbral (Type: directional, Intensity: 1.5, Lumen: Yes, Shadows: Yes)
[2025.08.27-18.03.43:729][288]LogTemp: HandleCreateDynamicLighting: Created light Sombras_Abismo_Umbral (Type: directional, Layer: 2, Intensity: 1.5, Success: Yes)
[2025.08.27-18.03.43:729][288]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_dynamic_lighting",
		"light_name": "Sombras_Abismo_Umbral",
		"light_type": "directional",
		"layer_index": 2,
		"intensity": 1.5,
		"use_lumen": true,
		"cast_shadows": true,
		"success": true,
		"timestamp": "2025.08.27-15.03.43",
		"location":
		{
			"x": 0,
			"y": 0,
			"z": 6000
		},
		"color":
		{
			"r": 0.40000000596046448,
			"g": 0.20000000298023224,
			"b": 0.80000001192092896,
			"a": 1
		}
	}
}
[2025.08.27-18.03.43:730][288]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 497
[2025.08.27-18.03.43:730][288]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.03.48:162][302]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.48:162][302]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.48:162][302]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.03.48:263][302]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.03.48:263][302]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.03.48:263][302]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.27-18.03.48:263][302]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.27-18.03.48:396][302]LogTemp: FUnrealMCPEditorCommands::HandleCommand - Processing: get_actors_in_level
[2025.08.27-18.03.48:397][302]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Planicie_Radiante_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Planicie_Radiante_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Planicie_Radiante_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Firmamento_Zephyr_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Firmamento_Zephyr_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Firmamento_Zephyr_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Abismo_Umbral_Terrain_Planicie_Radiante",
				"class": "Landscape",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Abismo_Umbral_Terrain_Firmamento_Zephyr",
				"class": "Landscape",
				"location": [ 0, 0, 2000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "Abismo_Umbral_Terrain_Abismo_Umbral",
				"class": "Landscape",
				"location": [ 0, 0, 4000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 128, 128, 256 ]
			},
			{
				"name": "FogOfWar_Layer_0_AURACRON_Fog_System",
				"class": "ExponentialHeightFog",
				"location": [ 0, 0, 1000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FogOfWar_Layer_1_AURACRON_Fog_System",
				"class": "ExponentialHeightFog",
				"location": [ 0, 0, 3000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "FogOfWar_Layer_2_AURACRON_Fog_System",
				"class": "ExponentialHeightFog",
				"location": [ 0, 0, 5000 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Sol_Planicie_Radiante",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Ventos_Firmamento_Zephyr",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Sombras_Abismo_Umbral",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		]
	}
}
[2025.08.27-18.03.48:397][302]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 3856
[2025.08.27-18.03.48:397][302]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.04.28:499][422]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.08.27-18.04.28:504][422]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/AURACRON" FILE="../../../../../../Game/AURACRON/Saved/Autosaves/Game/AURACRON_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.27-18.04.28:515][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/AURACRON_Auto1
[2025.08.27-18.04.28:515][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Auto140CF9BFA4BB0FDF2544B5682F6E6A6A8.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/AURACRON_Auto1.umap'
[2025.08.27-18.04.28:517][422]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/AURACRON' took 0.039
[2025.08.27-18.04.28:517][422]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.039
[2025.08.27-18.04.28:519][422]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer0/Material_Planicie_Radiante] ([1] browsable assets)...
[2025.08.27-18.04.31:101][422]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.08.27-18.04.31:171][422]OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer0/Material_Planicie_Radiante.Material_Planicie_Radiante]
[2025.08.27-18.04.31:171][422]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer0/Material_Planicie_Radiante]
[2025.08.27-18.04.31:189][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Auracron/Materials/Layer0/Material_Planicie_Radiante_Auto1
[2025.08.27-18.04.31:189][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_Planicie_Radiante_Auto16049BE7A4D4D6EACBE24D0B404EFB8E5.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/Auracron/Materials/Layer0/Material_Planicie_Radiante_Auto1.uasset'
[2025.08.27-18.04.31:191][422]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer0/Instances/Material_Planicie_Radiante_Instance] ([1] browsable assets)...
[2025.08.27-18.04.31:217][422]OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer0/Instances/Material_Planicie_Radiante_Instance.Material_Planicie_Radiante_Instance]
[2025.08.27-18.04.31:217][422]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer0/Instances/Material_Planicie_Radiante_Instance]
[2025.08.27-18.04.31:228][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Auracron/Materials/Layer0/Instances/Material_Planicie_Radiante_Instance_Auto1
[2025.08.27-18.04.31:228][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_Planicie_Radiante_Insta5A061BA94FBB71E79399BB9E2F541B95.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/Auracron/Materials/Layer0/Instances/Material_Planicie_Radiante_Instance_Auto1.uasset'
[2025.08.27-18.04.31:230][422]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer1/Material_Firmamento_Zephyr] ([1] browsable assets)...
[2025.08.27-18.04.33:007][422]OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer1/Material_Firmamento_Zephyr.Material_Firmamento_Zephyr]
[2025.08.27-18.04.33:007][422]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer1/Material_Firmamento_Zephyr]
[2025.08.27-18.04.33:017][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Auracron/Materials/Layer1/Material_Firmamento_Zephyr_Auto1
[2025.08.27-18.04.33:017][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_Firmamento_Zephyr_Auto16DC5B0654FB64C8ADC9A3F98644207F0.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/Auracron/Materials/Layer1/Material_Firmamento_Zephyr_Auto1.uasset'
[2025.08.27-18.04.33:018][422]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer1/Instances/Material_Firmamento_Zephyr_Instance] ([1] browsable assets)...
[2025.08.27-18.04.33:044][422]OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer1/Instances/Material_Firmamento_Zephyr_Instance.Material_Firmamento_Zephyr_Instance]
[2025.08.27-18.04.33:044][422]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer1/Instances/Material_Firmamento_Zephyr_Instance]
[2025.08.27-18.04.33:054][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Auracron/Materials/Layer1/Instances/Material_Firmamento_Zephyr_Instance_Auto1
[2025.08.27-18.04.33:054][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_Firmamento_Zephyr_InstaEC3B3B644BA9896310A412B396DF4817.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/Auracron/Materials/Layer1/Instances/Material_Firmamento_Zephyr_Instance_Auto1.uasset'
[2025.08.27-18.04.33:056][422]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer2/Material_Abismo_Umbral] ([1] browsable assets)...
[2025.08.27-18.04.34:948][422]OBJ SavePackage:     Rendered thumbnail for [Material /Game/Auracron/Materials/Layer2/Material_Abismo_Umbral.Material_Abismo_Umbral]
[2025.08.27-18.04.34:949][422]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer2/Material_Abismo_Umbral]
[2025.08.27-18.04.34:957][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Auracron/Materials/Layer2/Material_Abismo_Umbral_Auto1
[2025.08.27-18.04.34:957][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_Abismo_Umbral_Auto1A12F88034BE914B1488104B890F229BC.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/Auracron/Materials/Layer2/Material_Abismo_Umbral_Auto1.uasset'
[2025.08.27-18.04.34:959][422]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Auracron/Materials/Layer2/Instances/Material_Abismo_Umbral_Instance] ([1] browsable assets)...
[2025.08.27-18.04.34:984][422]OBJ SavePackage:     Rendered thumbnail for [MaterialInstanceConstant /Game/Auracron/Materials/Layer2/Instances/Material_Abismo_Umbral_Instance.Material_Abismo_Umbral_Instance]
[2025.08.27-18.04.34:984][422]OBJ SavePackage: Finished generating thumbnails for package [/Game/Auracron/Materials/Layer2/Instances/Material_Abismo_Umbral_Instance]
[2025.08.27-18.04.34:993][422]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Auracron/Materials/Layer2/Instances/Material_Abismo_Umbral_Instance_Auto1
[2025.08.27-18.04.34:993][422]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/Material_Abismo_Umbral_Instance_BBE6218A44604DC8541BAEAD7A67FE17.tmp' to '../../../../../../Game/AURACRON/Saved/Autosaves/Game/Auracron/Materials/Layer2/Instances/Material_Abismo_Umbral_Instance_Auto1.uasset'
[2025.08.27-18.04.34:994][422]LogFileHelpers: Auto-saving content packages took 6.477
[2025.08.27-18.04.35:028][422]LogAutomationController: Ignoring very large delta of 6.94 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.08.27-18.05.47:104][768]LogShaderCompilers: Display: ================================================
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Total job queries 332, among them cache hits 21 (6.33%), DDC hits 230 (69.28%), Duplicates 72 (21.69%)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Tracking 239 distinct input hashes that result in 186 distinct outputs (77.82%)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: RAM used: 1,24 MiB of 1,60 GiB budget. Usage: 0.08%
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Shaders Compiled: 9
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Jobs assigned 9, completed 9 (100%)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Average time worker was idle: 1.10 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Time job spent in pending queue: average 0.07 s, longest 0.11 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Job execution time: average 1.16 s, max 2.53 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Job life time (pending + execution): average 1.22 s, max 2.55
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Shader code size: total 55,312 KiB, numShaders 9, average 6,146 KiB, min 5,074 KiB, max 7,145 KiB
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 6.10 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.01%
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Jobs were issued in 9 batches (only local compilation was used), average 1.00 jobs/batch
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Average processing rate: 1.47 jobs/sec
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Total thread time: 9,346 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Total thread preprocess time: 4,896 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Percentage time preprocessing: 52.38%
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Effective parallelization: 1.53 (times faster than compiling all shaders on one thread). Compare with number of workers: 8 - 0.191412
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    3 times, average 1.85 sec, max 2.30 sec, min 1.59 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight (compiled    3 times, average 0.66 sec, max 0.67 sec, min 0.63 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    3 times, average 0.61 sec, max 0.65 sec, min 0.58 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialVS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialPS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                             FDebugViewModePS - 59.31% of total time (compiled    3 times, average 1.85 sec, max 2.30 sec, min 1.59 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 21.03% of total time (compiled    3 times, average 0.66 sec, max 0.67 sec, min 0.63 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 19.66% of total time (compiled    3 times, average 0.61 sec, max 0.65 sec, min 0.58 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialPS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display:                                      FLandscapeGrassWeightPS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: === Material stats ===
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Materials Cooked:        0
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Materials Translated:    141
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Material Total Translate Time: 0.13 s
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Material Translation Only: 0.04 s (31%)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (1%)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: Material Cache Hits: 10 (7%)
[2025.08.27-18.05.47:105][768]LogShaderCompilers: Display: ================================================
[2025.08.27-18.06.58:011][470]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.06.58:011][470]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.06.58:011][470]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.06.58:112][470]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.06.58:112][470]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.06.58:112][470]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_specialized_neutral_camps", "params": {"camp_system_name": "AURACRON_Neutral_Camps", "camp_types": [{"name": "Camp_Luz_Pequeno", "layer": "Planicie_Radiante", "size": "small", "monsters": ["Sprite_Luz", "Elemental_Menor"], "respawn_time": 60, "rewards": ["gold", "exp", "buff_luz_menor"]}, {"name": "Camp_Luz_Medio", "layer": "Planicie_Radiante", "size": "medium", "monsters": ["Guardiao_Cristal", "Sprites_Luz"], "respawn_time": 90, "rewards": ["gold", "exp", "buff_crescimento"]}, {"name": "Camp_Vento_Pequeno", "layer": "Firmamento_Zephyr", "size": "small", "monsters": ["Sylph_Menor", "Corrente_Vento"], "respawn_time": 75, "rewards": ["gold", "exp", "buff_velocidade"]}, {"name": "Camp_Vento_Medio", "layer": "Firmamento_Zephyr", "size": "medium", "monsters": ["Elemental_Tempestade", "Sylphs"], "respawn_time": 120, "rewards": ["gold", "exp", "buff_penetracao"]}, {"name": "Camp_Sombra_Pequeno", "layer": "Abismo_Umbral", "size": "small", "monsters": ["Sombra_Rastejante", "Wraith_Menor"], "respawn_time": 90, "rewards": ["gold", "exp", "buff_stealth"]}, {"name": "Camp_Sombra_Grande", "layer": "Abismo_Umbral", "size": "large", "monsters": ["Senhor_Sombras", "Wraiths_Elite"], "respawn_time": 180, "rewards": ["gold", "exp", "buff_dano_verdadeiro"]}], "spawn_schedules": {"small_camps": 60.0, "medium_camps": 90.0, "large_camps": 180.0}, "reward_systems": {"gold_multiplier": 1, "exp_multiplier": 1, "buff_duration": 180}, "difficulty_scaling": {"Planicie_Radiante": 1.0, "Firmamento_Zephyr": 1.2, "Abismo_Umbral": 1.5}}}
[2025.08.27-18.06.58:112][470]LogTemp: Display: UnrealMCPBridge: Executing command: create_specialized_neutral_camps
[2025.08.27-18.06.58:178][470]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_specialized_neutral_camps
[2025.08.27-18.06.58:178][470]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameter: neutral_camps_system_name"
}
[2025.08.27-18.06.58:178][470]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 92
[2025.08.27-18.06.58:178][470]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.07.18:818][532]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.07.18:818][532]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.07.18:818][532]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.07.18:919][533]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.07.18:919][533]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.07.18:919][533]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_specialized_neutral_camps", "params": {"camp_system_name": "AURACRON_Neutral_Camps", "camp_types": [{"name": "Camp_Luz_Pequeno", "monsters": ["Sprite_Luz", "Elemental_Menor"], "size": "small", "layer": "Planicie_Radiante", "respawn_time": 60, "rewards": ["gold", "exp", "buff_luz_menor"]}, {"name": "Camp_Luz_Medio", "monsters": ["Guardiao_Cristal", "Sprites_Luz"], "size": "medium", "layer": "Planicie_Radiante", "respawn_time": 90, "rewards": ["gold", "exp", "buff_crescimento"]}, {"name": "Camp_Vento_Pequeno", "monsters": ["Sylph_Menor", "Corrente_Vento"], "size": "small", "layer": "Firmamento_Zephyr", "respawn_time": 75, "rewards": ["gold", "exp", "buff_velocidade"]}, {"name": "Camp_Vento_Medio", "monsters": ["Elemental_Tempestade", "Sylphs"], "size": "medium", "layer": "Firmamento_Zephyr", "respawn_time": 120, "rewards": ["gold", "exp", "buff_penetracao"]}, {"name": "Camp_Sombra_Pequeno", "monsters": ["Sombra_Rastejante", "Wraith_Menor"], "size": "small", "layer": "Abismo_Umbral", "respawn_time": 90, "rewards": ["gold", "exp", "buff_stealth"]}, {"name": "Camp_Sombra_Grande", "monsters": ["Senhor_Sombras", "Wraiths_Elite"], "size": "large", "layer": "Abismo_Umbral", "respawn_time": 180, "rewards": ["gold", "exp", "buff_dano_verdadeiro"]}], "spawn_schedules": {"medium_camps": 90.0, "small_camps": 60.0, "large_camps": 180.0}, "reward_systems": {"buff_duration": 180, "exp_multiplier": 1, "gold_multiplier": 1}, "difficulty_scaling": {"Planicie_Radiante": 1.0, "Firmamento_Zephyr": 1.2, "Abismo_Umbral": 1.5}}}
[2025.08.27-18.07.18:920][533]LogTemp: Display: UnrealMCPBridge: Executing command: create_specialized_neutral_camps
[2025.08.27-18.07.19:181][533]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_specialized_neutral_camps
[2025.08.27-18.07.19:181][533]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameter: neutral_camps_system_name"
}
[2025.08.27-18.07.19:181][533]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 92
[2025.08.27-18.07.19:181][533]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.07.30:058][566]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.07.30:058][566]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.07.30:058][566]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.07.30:160][566]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.07.30:160][566]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.07.30:160][566]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_specialized_neutral_camps", "params": {"camp_system_name": "AURACRON_Neutral_Camps", "camp_types": [{"name": "Camp_Luz_Pequeno", "monsters": ["Sprite_Luz", "Elemental_Menor"], "size": "small", "layer": "Planicie_Radiante", "respawn_time": 60, "rewards": ["gold", "exp", "buff_luz_menor"]}, {"name": "Camp_Luz_Medio", "monsters": ["Guardiao_Cristal", "Sprites_Luz"], "size": "medium", "layer": "Planicie_Radiante", "respawn_time": 90, "rewards": ["gold", "exp", "buff_crescimento"]}], "spawn_schedules": {"medium_camps": 90.0, "small_camps": 60.0, "large_camps": 180.0}, "reward_systems": {"buff_duration": 180, "exp_multiplier": 1, "gold_multiplier": 1}, "difficulty_scaling": {"Planicie_Radiante": 1.0, "Firmamento_Zephyr": 1.2, "Abismo_Umbral": 1.5}}}
[2025.08.27-18.07.30:160][566]LogTemp: Display: UnrealMCPBridge: Executing command: create_specialized_neutral_camps
[2025.08.27-18.07.30:182][566]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_specialized_neutral_camps
[2025.08.27-18.07.30:182][566]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Missing required parameter: neutral_camps_system_name"
}
[2025.08.27-18.07.30:183][566]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 92
[2025.08.27-18.07.30:183][566]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.07.58:352][ 81]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.07.58:352][ 81]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.07.58:352][ 81]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.07.58:454][ 81]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.07.58:454][ 81]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.07.58:454][ 81]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_minion_spawning_system", "params": {"minion_system_name": "AURACRON_Minion_System", "spawn_points": [{"team": "blue", "layer": "Planicie_Radiante", "location": {"x": -8000, "y": 0, "z": 1000}}, {"team": "red", "layer": "Planicie_Radiante", "location": {"x": 8000, "y": 0, "z": 1000}}, {"team": "blue", "layer": "Firmamento_Zephyr", "location": {"x": -7000, "y": 0, "z": 3000}}, {"team": "red", "layer": "Firmamento_Zephyr", "location": {"x": 7000, "y": 0, "z": 3000}}, {"team": "blue", "layer": "Abismo_Umbral", "location": {"x": -6000, "y": 0, "z": 5000}}, {"team": "red", "layer": "Abismo_Umbral", "location": {"x": 6000, "y": 0, "z": 5000}}], "minion_types": [{"name": "Minion_Luz", "layer": "Planicie_Radiante", "health": 500, "damage": 50, "speed": 300}, {"name": "Minion_Vento", "layer": "Firmamento_Zephyr", "health": 600, "damage": 60, "speed": 350}, {"name": "Minion_Sombra", "layer": "Abismo_Umbral", "health": 700, "damage": 70, "speed": 250}], "routing_paths": [{"from": {"x": -8000, "y": 0, "z": 1000}, "to": {"x": 8000, "y": 0, "z": 1000}, "layer": "Planicie_Radiante"}, {"from": {"x": -7000, "y": 0, "z": 3000}, "to": {"x": 7000, "y": 0, "z": 3000}, "layer": "Firmamento_Zephyr"}, {"from": {"x": -6000, "y": 0, "z": 5000}, "to": {"x": 6000, "y": 0, "z": 5000}, "layer": "Abismo_Umbral"}], "spawn_intervals": {"wave_interval": 30.0, "minions_per_wave": 6.0}}}
[2025.08.27-18.07.58:455][ 81]LogTemp: Display: UnrealMCPBridge: Executing command: create_minion_spawning_system
[2025.08.27-18.07.58:609][ 81]LogTemp: FUnrealMCPMOBACommands::HandleCommand - Processing: create_minion_spawning_system
[2025.08.27-18.07.58:609][ 81]LogJson: Warning: Field layer_name was not found.
[2025.08.27-18.07.58:609][ 81]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Field minion_type was not found.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane0_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane1_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane2_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane0_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane1_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane2_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Field layer_name was not found.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Field minion_type was not found.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane0_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane1_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane2_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane0_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane1_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane2_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Field layer_name was not found.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Field minion_type was not found.
[2025.08.27-18.07.58:610][ 81]LogJson: Warning: Json Value of type 'Null' used as a 'String'.
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane0_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane1_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team0_Lane2_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane0_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane1_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System: Created spawning point AURACRON_Minion_System__Team1_Lane2_Spawner for  minions
[2025.08.27-18.07.58:610][ 81]LogEditorAssetSubsystem: Error: SaveAsset failed: Could not load asset: The AssetData '/Game/Auracron/MOBA/MinionSpawning/AURACRON_Minion_System.AURACRON_Minion_System' could not be found in the Asset Registry.
[2025.08.27-18.07.58:610][ 81]LogTemp: Minion Spawning System created: AURACRON_Minion_System (Spawning Points: 18, Types: 3, Saved: No)
[2025.08.27-18.07.58:612][ 81]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"minion_system_name": "AURACRON_Minion_System",
		"package_path": "/Game/Auracron/MOBA/MinionSpawning/AURACRON_Minion_System",
		"spawning_points_created": 18,
		"minion_types_count": 3,
		"saved_to_disk": false,
		"config_path": "../../../../../../Game/AURACRON/Content/Auracron/MOBA/MinionSpawning/AURACRON_Minion_System_Config.json",
		"configuration":
		{
			"minion_system_name": "AURACRON_Minion_System",
			"spawning_parameters":
			{
				"spawn_interval": 30,
				"minions_per_wave": 6,
				"enable_super_minions": true,
				"super_minion_frequency": 3
			},
			"minion_types": [
				{
					"name": "Minion_Luz",
					"layer": "Planicie_Radiante",
					"health": 500,
					"damage": 50,
					"speed": 300
				},
				{
					"name": "Minion_Vento",
					"layer": "Firmamento_Zephyr",
					"health": 600,
					"damage": 60,
					"speed": 350
				},
				{
					"name": "Minion_Sombra",
					"layer": "Abismo_Umbral",
					"health": 700,
					"damage": 70,
					"speed": 250
				}
			],
			"spawning_points_created": 18
		}
	}
}
[2025.08.27-18.07.58:612][ 81]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1117
[2025.08.27-18.07.58:612][ 81]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.27-18.08.10:885][118]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.08.10:885][118]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.08.10:885][118]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.27-18.08.10:985][119]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.27-18.08.10:985][119]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.27-18.08.10:985][119]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_base_buildings", "params": {"building_name": "Base_Azul_Planicie", "building_type": "nexus", "location": {"x": -8000.0, "y": 0.0, "z": 1000.0}, "team_side": "blue", "building_config": {"team_colors": true, "functional_elements": ["healing_fountain", "shop_access", "respawn_point"], "architectural_style": "crystal_radiant", "size": "large"}}}
[2025.08.27-18.08.10:985][119]LogTemp: Display: UnrealMCPBridge: Executing command: create_base_buildings
[2025.08.27-18.08.11:279][119]LogTemp: UnrealMCPArchitectureCommands::HandleCommand - Command: create_base_buildings
[2025.08.27-18.08.11:279][119]LogJson: Warning: Field layer_index was not found.
[2025.08.27-18.08.11:279][119]LogJson: Warning: Json Value of type 'Null' used as a 'Number'.
[2025.08.27-18.08.11:279][119]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.08.27-18.08.11:279][119]LogWindows: Error: appError called: Fatal error: [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\LevelActor.cpp] [Line: 585] 
Cannot generate unique name for 'Base_Azul_Planicie' in level 'Level /Game/AURACRON.AURACRON:PersistentLevel'.



[2025.08.27-18.08.11:279][119]LogWindows: Windows GetLastError: A operação foi concluída com êxito. (0)
[2025.08.27-18.08.12:617][119]LogWindows: Error: === Critical error: ===
[2025.08.27-18.08.12:617][119]LogWindows: Error: 
[2025.08.27-18.08.12:617][119]LogWindows: Error: Fatal error: [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\LevelActor.cpp] [Line: 585] 
[2025.08.27-18.08.12:617][119]LogWindows: Error: Cannot generate unique name for 'Base_Azul_Planicie' in level 'Level /Game/AURACRON.AURACRON:PersistentLevel'.
[2025.08.27-18.08.12:617][119]LogWindows: Error: 
[2025.08.27-18.08.12:617][119]LogWindows: Error: 
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff0e7544fe UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff0e75489f UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00000237024633fa UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::CreateModularArchitecture() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:860]
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00000237024630ed UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::CreateHierarchicalBuilding() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:726]
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x000002370246555b UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::HandleCreateBaseBuildings() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:220]
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x0000023702464f94 UnrealEditor-UnrealMCP.dll!UUnrealMCPArchitectureCommands::HandleCommand() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp:71]
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x0000023702440acd UnrealEditor-UnrealMCP.dll!`UUnrealMCPBridge::ExecuteCommand'::`2'::<lambda_1>::operator()() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp:348]
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff1a9bcd83 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff1a9dfb72 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff1a9d284f UnrealEditor-Core.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff1a9d2ece UnrealEditor-Core.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff1afc1aa4 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff1afc35bf UnrealEditor-Core.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ffec6610b86 UnrealEditor-MassEntityEditor.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff17fc37cb UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff0f802c25 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff18571ff7 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fff19245956 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ff6b75c9ce4 UnrealEditor.exe!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ff6b75ee5ac UnrealEditor.exe!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ff6b75ee6ba UnrealEditor.exe!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ff6b75f209e UnrealEditor.exe!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ff6b7604e44 UnrealEditor.exe!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007ff6b76080fa UnrealEditor.exe!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: [Callstack] 0x00007fffec69e8d7 KERNEL32.DLL!UnknownFunction []
[2025.08.27-18.08.12:617][119]LogWindows: Error: 
[2025.08.27-18.08.12:628][119]LogExit: Executing StaticShutdownAfterError
[2025.08.27-18.08.12:634][119]LogWindows: FPlatformMisc::RequestExit(1, LaunchWindowsStartup.ExceptionHandler)
[2025.08.27-18.08.12:634][119]LogWindows: FPlatformMisc::RequestExitWithStatus(1, 3, LaunchWindowsStartup.ExceptionHandler)
[2025.08.27-18.08.12:634][119]LogCore: Engine exit requested (reason: Win RequestExit)
