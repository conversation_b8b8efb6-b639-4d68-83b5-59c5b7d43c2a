#pragma once

#include "CoreMinimal.h"
#include "Json.h"
#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"

// ========================================
// THREAD SAFETY AND MEMORY LEAK PREVENTION MACROS - MODERN UE 5.6.1
// ========================================

/**
 * Macro for mandatory thread safety validation in all asset creation functions
 * OBRIGATÓRIO: Must be used at the beginning of every function that creates assets
 */
#define AURACRON_VALIDATE_GAME_THREAD() \
    if (!IsInGameThread()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("%s: THREAD SAFETY VIOLATION - Must be called from game thread. Current thread: %s"), \
               *FString(__FUNCTION__), IsInRenderingThread() ? TEXT("Rendering") : TEXT("Unknown")); \
        return nullptr; \
    }

/**
 * Macro for mandatory thread safety validation in functions that return JSON
 * OBRIGATÓRIO: Must be used at the beginning of every command handler function
 */
#define AURACRON_VALIDATE_GAME_THREAD_JSON() \
    if (!IsInGameThread()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("%s: THREAD SAFETY VIOLATION - Must be called from game thread. Current thread: %s"), \
               *FString(__FUNCTION__), IsInRenderingThread() ? TEXT("Rendering") : TEXT("Unknown")); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Thread safety violation - must execute on Game Thread")); \
    }

/**
 * Macro for mandatory thread safety validation in functions that return int32
 * OBRIGATÓRIO: Must be used at the beginning of every function that returns int32
 */
#define AURACRON_VALIDATE_GAME_THREAD_INT32() \
    if (!IsInGameThread()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("%s: THREAD SAFETY VIOLATION - Must be called from game thread. Current thread: %s"), \
               *FString(__FUNCTION__), IsInRenderingThread() ? TEXT("Rendering") : TEXT("Unknown")); \
        return 0; \
    }

/**
 * Macro for mandatory parameter validation
 * OBRIGATÓRIO: Must be used to validate all input parameters
 */
#define AURACRON_VALIDATE_PARAMETERS(condition, error_message) \
    if (!(condition)) \
    { \
        UE_LOG(LogTemp, Error, TEXT("%s: PARAMETER VALIDATION FAILED - %s"), *FString(__FUNCTION__), TEXT(error_message)); \
        return nullptr; \
    }

/**
 * Macro for mandatory parameter validation in JSON functions
 * OBRIGATÓRIO: Must be used to validate all input parameters in command handlers
 */
#define AURACRON_VALIDATE_PARAMETERS_JSON(condition, error_message) \
    if (!(condition)) \
    { \
        UE_LOG(LogTemp, Error, TEXT("%s: PARAMETER VALIDATION FAILED - %s"), *FString(__FUNCTION__), TEXT(error_message)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT(error_message)); \
    }

/**
 * Macro for mandatory memory leak prevention
 * OBRIGATÓRIO: Must be used when creating UObjects to ensure proper cleanup
 */
#define AURACRON_SAFE_OBJECT_CREATION(ObjectType, Outer, Name, Flags) \
    ([&]() -> ObjectType* { \
        ObjectType* NewObject = NewObject<ObjectType>(Outer, Name, Flags); \
        if (!NewObject || !IsValid(NewObject)) \
        { \
            UE_LOG(LogTemp, Error, TEXT("%s: MEMORY ALLOCATION FAILED - Failed to create %s"), \
                   *FString(__FUNCTION__), TEXT(#ObjectType)); \
            return nullptr; \
        } \
        return NewObject; \
    })()

/**
 * Macro for mandatory asset validation after creation
 * OBRIGATÓRIO: Must be used after creating any asset to verify it was properly saved
 */
#define AURACRON_VALIDATE_ASSET_CREATION(AssetPath, AssetType, AssetName) \
    if (!FUnrealMCPCommonUtils::ValidateAndLogAssetCreation(AssetPath, AssetType, AssetName, FString(__FUNCTION__))) \
    { \
        UE_LOG(LogTemp, Error, TEXT("%s: ASSET VALIDATION FAILED - %s asset '%s' was not properly created"), \
               *FString(__FUNCTION__), *AssetType, *AssetName); \
        return nullptr; \
    }

/**
 * Macro for mandatory async task execution with thread safety
 * OBRIGATÓRIO: Must be used for any operations that might need to run on game thread
 */
#define AURACRON_EXECUTE_ON_GAME_THREAD(Lambda) \
    if (!IsInGameThread()) \
    { \
        AsyncTask(ENamedThreads::GameThread, Lambda); \
        return; \
    } \
    Lambda();

// Forward declarations
class AActor;
class UBlueprint;
class UEdGraph;
class UEdGraphNode;
class UEdGraphPin;
class UK2Node_Event;
class UK2Node_CallFunction;
class UK2Node_VariableGet;
class UK2Node_VariableSet;
class UK2Node_InputAction;
class UK2Node_Self;
class UFunction;

/**
 * Common utilities for UnrealMCP commands
 */
class UNREALMCP_API FUnrealMCPCommonUtils
{
public:
    // JSON utilities
    static TSharedPtr<FJsonObject> CreateErrorResponse(const FString& Message);
    static TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data = nullptr);
    static void GetIntArrayFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName, TArray<int32>& OutArray);
    static void GetFloatArrayFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName, TArray<float>& OutArray);
    static FVector2D GetVector2DFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName);
    static FVector GetVectorFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName);
    static FRotator GetRotatorFromJson(const TSharedPtr<FJsonObject>& JsonObject, const FString& FieldName);
    
    // Actor utilities
    static TSharedPtr<FJsonValue> ActorToJson(AActor* Actor);
    static TSharedPtr<FJsonObject> ActorToJsonObject(AActor* Actor, bool bDetailed = false);
    
    // Blueprint utilities
    static UBlueprint* FindBlueprint(const FString& BlueprintName);
    static UBlueprint* FindBlueprintByName(const FString& BlueprintName);
    static UEdGraph* FindOrCreateEventGraph(UBlueprint* Blueprint);
    
    // Blueprint node utilities
    static UK2Node_Event* CreateEventNode(UEdGraph* Graph, const FString& EventName, const FVector2D& Position);
    static UK2Node_CallFunction* CreateFunctionCallNode(UEdGraph* Graph, UFunction* Function, const FVector2D& Position);
    static UK2Node_VariableGet* CreateVariableGetNode(UEdGraph* Graph, UBlueprint* Blueprint, const FString& VariableName, const FVector2D& Position);
    static UK2Node_VariableSet* CreateVariableSetNode(UEdGraph* Graph, UBlueprint* Blueprint, const FString& VariableName, const FVector2D& Position);
    static UK2Node_InputAction* CreateInputActionNode(UEdGraph* Graph, const FString& ActionName, const FVector2D& Position);
    static UK2Node_Self* CreateSelfReferenceNode(UEdGraph* Graph, const FVector2D& Position);
    static bool ConnectGraphNodes(UEdGraph* Graph, UEdGraphNode* SourceNode, const FString& SourcePinName, 
                                UEdGraphNode* TargetNode, const FString& TargetPinName);
    static UEdGraphPin* FindPin(UEdGraphNode* Node, const FString& PinName, EEdGraphPinDirection Direction = EGPD_MAX);
    static UK2Node_Event* FindExistingEventNode(UEdGraph* Graph, const FString& EventName);

    // Property utilities
    static bool SetObjectProperty(UObject* Object, const FString& PropertyName,
                                 const TSharedPtr<FJsonValue>& Value, FString& OutErrorMessage);

    // ========================================
    // AURACRON-SPECIFIC UTILITY METHODS
    // ========================================

    /**
     * Get the name of an Auracron layer by index
     * @param LayerIndex The layer index (0=Planície Radiante, 1=Firmamento Zephyr, 2=Abismo Umbral)
     * @return The layer name string
     */
    static FString GetAuracronLayerName(int32 LayerIndex);

    /**
     * Get the world height of an Auracron layer
     * @param LayerIndex The layer index
     * @return The world Z coordinate for the layer
     */
    static float GetAuracronLayerHeight(int32 LayerIndex);

    /**
     * Get the color associated with an Auracron layer
     * @param LayerIndex The layer index
     * @return The layer's representative color
     */
    static FLinearColor GetAuracronLayerColor(int32 LayerIndex);

    /**
     * Check if a layer index is valid for Auracron
     * @param LayerIndex The layer index to validate
     * @return true if the layer index is valid (0-2)
     */
    static bool IsValidAuracronLayer(int32 LayerIndex);

    /**
     * Determine which Auracron layer a world height belongs to
     * @param Height The world Z coordinate
     * @return The layer index that contains this height
     */
    static int32 GetAuracronLayerFromHeight(float Height);

    /**
     * Convert world coordinates to layer-specific coordinates
     * @param WorldLocation The world location to convert
     * @param TargetLayer The target layer index
     * @return The converted location for the target layer
     */
    static FVector ConvertToAuracronLayerCoordinates(const FVector& WorldLocation, int32 TargetLayer);

    /**
     * Create comprehensive information about an Auracron layer
     * @param LayerIndex The layer index
     * @return JSON object containing detailed layer information
     */
    static TSharedPtr<FJsonObject> CreateAuracronLayerInfo(int32 LayerIndex);

    // ========================================
    // ASSET VALIDATION FUNCTIONS - MODERN UE 5.6.1 APIS
    // ========================================

    /**
     * Validate that an asset was actually created and saved to disk
     * @param AssetPath The asset path to validate
     * @param AssetType The expected asset type (for logging)
     * @param AssetName The asset name (for logging)
     * @return true if asset exists on disk, false otherwise
     */
    static bool ValidateAssetCreation(const FString& AssetPath, const FString& AssetType, const FString& AssetName);

    /**
     * Validate multiple assets were created and saved to disk
     * @param AssetPaths Array of asset paths to validate
     * @param AssetType The expected asset type (for logging)
     * @return true if all assets exist on disk, false otherwise
     */
    static bool ValidateMultipleAssetCreation(const TArray<FString>& AssetPaths, const FString& AssetType);

    /**
     * Get full disk path for an asset
     * @param AssetPath The asset path (e.g., "/Game/Auracron/Materials/MyMaterial")
     * @return Full disk path to the .uasset file
     */
    static FString GetAssetDiskPath(const FString& AssetPath);

    /**
     * Create detailed asset validation response for JSON
     * @param AssetPath The asset path
     * @param AssetType The asset type
     * @param AssetName The asset name
     * @param bCreationSuccess Whether the creation was successful
     * @return JSON object with detailed validation information
     */
    static TSharedPtr<FJsonObject> CreateAssetValidationResponse(const FString& AssetPath, const FString& AssetType, const FString& AssetName, bool bCreationSuccess);

    /**
     * Validate and log asset creation with detailed information
     * @param AssetPath The asset path to validate
     * @param AssetType The asset type
     * @param AssetName The asset name
     * @param FunctionName The function that created the asset
     * @return true if validation passed, false otherwise
     */
    static bool ValidateAndLogAssetCreation(const FString& AssetPath, const FString& AssetType, const FString& AssetName, const FString& FunctionName);
};